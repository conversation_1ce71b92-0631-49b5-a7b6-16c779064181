/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Finnovars_lab%2FDownloads%2Fquote-ai-crm-starter%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Finnovars_lab%2FDownloads%2Fquote-ai-crm-starter&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Finnovars_lab%2FDownloads%2Fquote-ai-crm-starter%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Finnovars_lab%2FDownloads%2Fquote-ai-crm-starter&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(rsc)/./app/page.tsx\")), \"/Users/<USER>/Downloads/quote-ai-crm-starter/app/page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"/Users/<USER>/Downloads/quote-ai-crm-starter/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Downloads/quote-ai-crm-starter/app/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Finnovars_lab%2FDownloads%2Fquote-ai-crm-starter%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Finnovars_lab%2FDownloads%2Fquote-ai-crm-starter&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Finnovars_lab%2FDownloads%2Fquote-ai-crm-starter%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Finnovars_lab%2FDownloads%2Fquote-ai-crm-starter%2Fnode_modules%2Fsonner%2Fdist%2Findex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Finnovars_lab%2FDownloads%2Fquote-ai-crm-starter%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Finnovars_lab%2FDownloads%2Fquote-ai-crm-starter%2Fnode_modules%2Fsonner%2Fdist%2Findex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/sonner/dist/index.mjs */ \"(ssr)/./node_modules/sonner/dist/index.mjs\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGaW5ub3ZhcnNfbGFiJTJGRG93bmxvYWRzJTJGcXVvdGUtYWktY3JtLXN0YXJ0ZXIlMkZhcHAlMkZnbG9iYWxzLmNzcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjIlMkZVc2VycyUyRmlubm92YXJzX2xhYiUyRkRvd25sb2FkcyUyRnF1b3RlLWFpLWNybS1zdGFydGVyJTJGbm9kZV9tb2R1bGVzJTJGc29ubmVyJTJGZGlzdCUyRmluZGV4Lm1qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMlRvYXN0ZXIlMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLG9MQUF1SiIsInNvdXJjZXMiOlsid2VicGFjazovL3F1b3RlLWFpLWNybS8/ZDBkYyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIlRvYXN0ZXJcIl0gKi8gXCIvVXNlcnMvaW5ub3ZhcnNfbGFiL0Rvd25sb2Fkcy9xdW90ZS1haS1jcm0tc3RhcnRlci9ub2RlX21vZHVsZXMvc29ubmVyL2Rpc3QvaW5kZXgubWpzXCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Finnovars_lab%2FDownloads%2Fquote-ai-crm-starter%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Finnovars_lab%2FDownloads%2Fquote-ai-crm-starter%2Fnode_modules%2Fsonner%2Fdist%2Findex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Finnovars_lab%2FDownloads%2Fquote-ai-crm-starter%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Finnovars_lab%2FDownloads%2Fquote-ai-crm-starter%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(ssr)/./app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGaW5ub3ZhcnNfbGFiJTJGRG93bmxvYWRzJTJGcXVvdGUtYWktY3JtLXN0YXJ0ZXIlMkZhcHAlMkZwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsd0lBQW9HIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcXVvdGUtYWktY3JtLz85ZTE4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL2lubm92YXJzX2xhYi9Eb3dubG9hZHMvcXVvdGUtYWktY3JtLXN0YXJ0ZXIvYXBwL3BhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Finnovars_lab%2FDownloads%2Fquote-ai-crm-starter%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Finnovars_lab%2FDownloads%2Fquote-ai-crm-starter%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Finnovars_lab%2FDownloads%2Fquote-ai-crm-starter%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Finnovars_lab%2FDownloads%2Fquote-ai-crm-starter%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Finnovars_lab%2FDownloads%2Fquote-ai-crm-starter%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Finnovars_lab%2FDownloads%2Fquote-ai-crm-starter%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Finnovars_lab%2FDownloads%2Fquote-ai-crm-starter%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Finnovars_lab%2FDownloads%2Fquote-ai-crm-starter%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Finnovars_lab%2FDownloads%2Fquote-ai-crm-starter%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Finnovars_lab%2FDownloads%2Fquote-ai-crm-starter%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Finnovars_lab%2FDownloads%2Fquote-ai-crm-starter%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Finnovars_lab%2FDownloads%2Fquote-ai-crm-starter%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Finnovars_lab%2FDownloads%2Fquote-ai-crm-starter%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Finnovars_lab%2FDownloads%2Fquote-ai-crm-starter%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Finnovars_lab%2FDownloads%2Fquote-ai-crm-starter%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Finnovars_lab%2FDownloads%2Fquote-ai-crm-starter%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Finnovars_lab%2FDownloads%2Fquote-ai-crm-starter%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Finnovars_lab%2FDownloads%2Fquote-ai-crm-starter%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Finnovars_lab%2FDownloads%2Fquote-ai-crm-starter%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_checkoutStripe__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/checkoutStripe */ \"(ssr)/./lib/checkoutStripe.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction Home() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"min-h-screen bg-gradient-to-br from-[#0f0c29] via-[#302b63] to-[#24243e] text-white font-sans\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"text-center py-24 px-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-4xl md:text-6xl font-bold mb-4\",\n                        children: [\n                            \"Offertes in 60 seconden met AI-assistent \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-fuchsia-400\",\n                                children: \"Rima\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/quote-ai-crm-starter/app/page.tsx\",\n                                lineNumber: 8,\n                                columnNumber: 102\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/quote-ai-crm-starter/app/page.tsx\",\n                        lineNumber: 8,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-lg md:text-xl mb-8 text-white/80\",\n                        children: \"Bespaar tijd, win klanten en beheer je bouwbedrijf volledig online.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/quote-ai-crm-starter/app/page.tsx\",\n                        lineNumber: 9,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>(0,_lib_checkoutStripe__WEBPACK_IMPORTED_MODULE_2__.startStripeCheckout)(\"starter\"),\n                                className: \"px-6 py-3 rounded-xl bg-gradient-to-r from-blue-500 to-fuchsia-500 text-white font-semibold shadow-xl\",\n                                children: \"Start Gratis Proefperiode\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/quote-ai-crm-starter/app/page.tsx\",\n                                lineNumber: 11,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"#demo\",\n                                className: \"px-6 py-3 rounded-xl border border-white/20 hover:bg-white/10\",\n                                children: \"Bekijk Demo\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/quote-ai-crm-starter/app/page.tsx\",\n                                lineNumber: 12,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/quote-ai-crm-starter/app/page.tsx\",\n                        lineNumber: 10,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-sm text-white/50\",\n                        children: \"14 dagen gratis • Geen kredietkaart nodig • Daarna automatisch €24/maand via Stripe\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/quote-ai-crm-starter/app/page.tsx\",\n                        lineNumber: 14,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/quote-ai-crm-starter/app/page.tsx\",\n                lineNumber: 7,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"max-w-5xl mx-auto grid grid-cols-2 md:grid-cols-4 gap-6 px-6 py-12 text-center\",\n                children: [\n                    \"+2500\",\n                    \"+18u/week\",\n                    \"93%\",\n                    \"60s\"\n                ].map((stat, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white/5 border border-white/10 p-6 rounded-2xl backdrop-blur-xl\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-3xl font-bold text-fuchsia-300\",\n                                children: stat\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/quote-ai-crm-starter/app/page.tsx\",\n                                lineNumber: 19,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-white/70 text-sm mt-2\",\n                                children: [\n                                    \"Gebruikers\",\n                                    \"Tijdswinst\",\n                                    \"Tevredenheid\",\n                                    \"Offerte klaar\"\n                                ][i]\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/quote-ai-crm-starter/app/page.tsx\",\n                                lineNumber: 20,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, i, true, {\n                        fileName: \"/Users/<USER>/Downloads/quote-ai-crm-starter/app/page.tsx\",\n                        lineNumber: 18,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/quote-ai-crm-starter/app/page.tsx\",\n                lineNumber: 16,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 px-6 max-w-6xl mx-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-3xl font-bold text-center mb-6\",\n                        children: \"Pakketten & Prijzen\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/quote-ai-crm-starter/app/page.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-center text-white/70 mb-12\",\n                        children: \"Start gratis. Groepeer je offertes, klanten en projecten.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/quote-ai-crm-starter/app/page.tsx\",\n                        lineNumber: 26,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid md:grid-cols-4 gap-6\",\n                        children: [\n                            {\n                                title: \"Free\",\n                                price: \"€0\",\n                                features: [\n                                    \"Max 3 offertes\",\n                                    \"E-mail verzending\"\n                                ],\n                                button: \"Start Gratis\",\n                                link: \"/register\"\n                            },\n                            {\n                                title: \"Starter\",\n                                price: \"€24/m\",\n                                features: [\n                                    \"Tot 20 offertes/maand\",\n                                    \"E-mail opvolging\",\n                                    \"Basis CRM\"\n                                ],\n                                button: \"14 dagen gratis proef • Daarna €24/m via Stripe\",\n                                plan: \"starter\"\n                            },\n                            {\n                                title: \"Professional\",\n                                price: \"€49/m\",\n                                features: [\n                                    \"Alles van Starter\",\n                                    \"PDF facturen\",\n                                    \"AI suggesties\"\n                                ],\n                                button: \"Start Professional\",\n                                plan: \"pro\"\n                            },\n                            {\n                                title: \"Enterprise\",\n                                price: \"Custom\",\n                                features: [\n                                    \"Volledige automatisatie\",\n                                    \"Eigen domein\",\n                                    \"24/7 support\"\n                                ],\n                                button: \"Contact Sales\",\n                                link: \"/contact\"\n                            }\n                        ].map((p, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white/5 border border-white/10 p-6 rounded-2xl backdrop-blur-xl\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-semibold mb-2\",\n                                        children: p.title\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/quote-ai-crm-starter/app/page.tsx\",\n                                        lineNumber: 35,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl font-bold mb-4\",\n                                        children: p.price\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/quote-ai-crm-starter/app/page.tsx\",\n                                        lineNumber: 36,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"text-sm text-white/80 space-y-2 mb-6\",\n                                        children: p.features.map((f, j)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: [\n                                                    \"✓ \",\n                                                    f\n                                                ]\n                                            }, j, true, {\n                                                fileName: \"/Users/<USER>/Downloads/quote-ai-crm-starter/app/page.tsx\",\n                                                lineNumber: 38,\n                                                columnNumber: 44\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/quote-ai-crm-starter/app/page.tsx\",\n                                        lineNumber: 37,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"plan\" in p ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>(0,_lib_checkoutStripe__WEBPACK_IMPORTED_MODULE_2__.startStripeCheckout)(p.plan),\n                                        className: \"block w-full px-4 py-2 text-sm rounded-lg text-center bg-gradient-to-r from-blue-500 to-fuchsia-500 text-white shadow-md\",\n                                        children: p.button\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/quote-ai-crm-starter/app/page.tsx\",\n                                        lineNumber: 41,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: p.link,\n                                        className: \"block w-full px-4 py-2 text-sm rounded-lg text-center bg-gradient-to-r from-blue-500 to-fuchsia-500 text-white shadow-md\",\n                                        children: p.button\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/quote-ai-crm-starter/app/page.tsx\",\n                                        lineNumber: 43,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, i, true, {\n                                fileName: \"/Users/<USER>/Downloads/quote-ai-crm-starter/app/page.tsx\",\n                                lineNumber: 34,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/quote-ai-crm-starter/app/page.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/quote-ai-crm-starter/app/page.tsx\",\n                lineNumber: 24,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"demo\",\n                className: \"py-20 px-6 max-w-5xl mx-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-3xl font-bold text-center mb-10\",\n                        children: \"Hoe Rima jouw offerte maakt\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/quote-ai-crm-starter/app/page.tsx\",\n                        lineNumber: 50,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid md:grid-cols-4 gap-6\",\n                        children: [\n                            \"Omschrijf je project\",\n                            \"AI maakt een voorstel\",\n                            \"Pas aan indien nodig\",\n                            \"Genereer PDF & verzend\"\n                        ].map((step, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white/5 border border-white/10 p-6 rounded-2xl backdrop-blur-xl text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-4xl font-bold text-blue-300 mb-2\",\n                                        children: i + 1\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/quote-ai-crm-starter/app/page.tsx\",\n                                        lineNumber: 54,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-white/80\",\n                                        children: step\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/quote-ai-crm-starter/app/page.tsx\",\n                                        lineNumber: 55,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, i, true, {\n                                fileName: \"/Users/<USER>/Downloads/quote-ai-crm-starter/app/page.tsx\",\n                                lineNumber: 53,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/quote-ai-crm-starter/app/page.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/quote-ai-crm-starter/app/page.tsx\",\n                lineNumber: 49,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"bg-white/5 border-t border-white/10 py-10 px-6 text-center text-sm text-white/60\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: [\n                            \"\\xa9 \",\n                            new Date().getFullYear(),\n                            \" Quote.AI+CRM — Rima\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/quote-ai-crm-starter/app/page.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-3 flex justify-center gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"#\",\n                                className: \"hover:underline\",\n                                children: \"Support\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/quote-ai-crm-starter/app/page.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"#\",\n                                className: \"hover:underline\",\n                                children: \"Privacy\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/quote-ai-crm-starter/app/page.tsx\",\n                                lineNumber: 64,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"#\",\n                                className: \"hover:underline\",\n                                children: \"Terms\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/quote-ai-crm-starter/app/page.tsx\",\n                                lineNumber: 65,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/quote-ai-crm-starter/app/page.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/quote-ai-crm-starter/app/page.tsx\",\n                lineNumber: 60,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Downloads/quote-ai-crm-starter/app/page.tsx\",\n        lineNumber: 6,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/checkoutStripe.ts":
/*!*******************************!*\
  !*** ./lib/checkoutStripe.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   startStripeCheckout: () => (/* binding */ startStripeCheckout)\n/* harmony export */ });\n/* harmony import */ var _stripe_stripe_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @stripe/stripe-js */ \"(ssr)/./node_modules/@stripe/stripe-js/lib/index.mjs\");\n\n// Initialize Stripe\nconst stripePromise = (0,_stripe_stripe_js__WEBPACK_IMPORTED_MODULE_0__.loadStripe)(\"pk_test_your_stripe_publishable_key\");\nasync function startStripeCheckout(plan) {\n    try {\n        const stripe = await stripePromise;\n        if (!stripe) {\n            throw new Error(\"Stripe failed to initialize\");\n        }\n        // Create checkout session\n        const response = await fetch(\"/api/create-checkout-session\", {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                plan\n            })\n        });\n        if (!response.ok) {\n            throw new Error(\"Failed to create checkout session\");\n        }\n        const { sessionId } = await response.json();\n        // Redirect to Stripe Checkout\n        const { error } = await stripe.redirectToCheckout({\n            sessionId\n        });\n        if (error) {\n            console.error(\"Stripe checkout error:\", error);\n            throw error;\n        }\n    } catch (error) {\n        console.error(\"Checkout error:\", error);\n        // You might want to show a toast notification here\n        alert(\"Er is een fout opgetreden bij het starten van de checkout. Probeer het opnieuw.\");\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/checkoutStripe.ts\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"b44dba317bee\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9xdW90ZS1haS1jcm0vLi9hcHAvZ2xvYmFscy5jc3M/NmViYyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImI0NGRiYTMxN2JlZVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! sonner */ \"(rsc)/./node_modules/sonner/dist/index.mjs\");\n\nconst metadata = {\n    title: \"Quote.AI+CRM\",\n    description: \"AI-offertes, CRM en automatisering\"\n};\n\n\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"nl\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            children: [\n                children,\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(sonner__WEBPACK_IMPORTED_MODULE_2__.Toaster, {\n                    richColors: true,\n                    position: \"top-center\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/quote-ai-crm-starter/app/layout.tsx\",\n                    lineNumber: 9,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Downloads/quote-ai-crm-starter/app/layout.tsx\",\n            lineNumber: 7,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Downloads/quote-ai-crm-starter/app/layout.tsx\",\n        lineNumber: 6,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQU8sTUFBTUEsV0FBVztJQUFFQyxPQUFPO0lBQWdCQyxhQUFhO0FBQXFDLEVBQUU7QUFDOUU7QUFDVTtBQUNsQixTQUFTRSxXQUFXLEVBQUVDLFFBQVEsRUFBaUM7SUFDNUUscUJBQ0UsOERBQUNDO1FBQUtDLE1BQUs7a0JBQ1QsNEVBQUNDOztnQkFDRUg7OEJBQ0QsOERBQUNGLDJDQUFPQTtvQkFBQ00sVUFBVTtvQkFBQ0MsVUFBUzs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFJckMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9xdW90ZS1haS1jcm0vLi9hcHAvbGF5b3V0LnRzeD85OTg4Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCBtZXRhZGF0YSA9IHsgdGl0bGU6IFwiUXVvdGUuQUkrQ1JNXCIsIGRlc2NyaXB0aW9uOiBcIkFJLW9mZmVydGVzLCBDUk0gZW4gYXV0b21hdGlzZXJpbmdcIiB9O1xuaW1wb3J0IFwiLi9nbG9iYWxzLmNzc1wiO1xuaW1wb3J0IHsgVG9hc3RlciB9IGZyb20gXCJzb25uZXJcIjtcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoeyBjaGlsZHJlbiB9OiB7IGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGUgfSkge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJubFwiPlxuICAgICAgPGJvZHk+XG4gICAgICAgIHtjaGlsZHJlbn1cbiAgICAgICAgPFRvYXN0ZXIgcmljaENvbG9ycyBwb3NpdGlvbj1cInRvcC1jZW50ZXJcIiAvPlxuICAgICAgPC9ib2R5PlxuICAgIDwvaHRtbD5cbiAgKTtcbn0iXSwibmFtZXMiOlsibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiVG9hc3RlciIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsImh0bWwiLCJsYW5nIiwiYm9keSIsInJpY2hDb2xvcnMiLCJwb3NpdGlvbiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Downloads/quote-ai-crm-starter/app/page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Downloads/quote-ai-crm-starter/app/page.tsx#default`));


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/sonner","vendor-chunks/@stripe"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Finnovars_lab%2FDownloads%2Fquote-ai-crm-starter%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Finnovars_lab%2FDownloads%2Fquote-ai-crm-starter&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();