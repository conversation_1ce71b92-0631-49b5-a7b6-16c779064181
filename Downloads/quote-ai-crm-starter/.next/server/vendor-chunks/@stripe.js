"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@stripe";
exports.ids = ["vendor-chunks/@stripe"];
exports.modules = {

/***/ "(ssr)/./node_modules/@stripe/stripe-js/dist/index.mjs":
/*!*******************************************************!*\
  !*** ./node_modules/@stripe/stripe-js/dist/index.mjs ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   loadStripe: () => (/* binding */ loadStripe)\n/* harmony export */ });\nvar RELEASE_TRAIN = 'basil';\n\nvar runtimeVersionToUrlVersion = function runtimeVersionToUrlVersion(version) {\n  return version === 3 ? 'v3' : version;\n};\n\nvar ORIGIN = 'https://js.stripe.com';\nvar STRIPE_JS_URL = \"\".concat(ORIGIN, \"/\").concat(RELEASE_TRAIN, \"/stripe.js\");\nvar V3_URL_REGEX = /^https:\\/\\/js\\.stripe\\.com\\/v3\\/?(\\?.*)?$/;\nvar STRIPE_JS_URL_REGEX = /^https:\\/\\/js\\.stripe\\.com\\/(v3|[a-z]+)\\/stripe\\.js(\\?.*)?$/;\nvar EXISTING_SCRIPT_MESSAGE = 'loadStripe.setLoadParameters was called but an existing Stripe.js script already exists in the document; existing script parameters will be used';\n\nvar isStripeJSURL = function isStripeJSURL(url) {\n  return V3_URL_REGEX.test(url) || STRIPE_JS_URL_REGEX.test(url);\n};\n\nvar findScript = function findScript() {\n  var scripts = document.querySelectorAll(\"script[src^=\\\"\".concat(ORIGIN, \"\\\"]\"));\n\n  for (var i = 0; i < scripts.length; i++) {\n    var script = scripts[i];\n\n    if (!isStripeJSURL(script.src)) {\n      continue;\n    }\n\n    return script;\n  }\n\n  return null;\n};\n\nvar injectScript = function injectScript(params) {\n  var queryString = params && !params.advancedFraudSignals ? '?advancedFraudSignals=false' : '';\n  var script = document.createElement('script');\n  script.src = \"\".concat(STRIPE_JS_URL).concat(queryString);\n  var headOrBody = document.head || document.body;\n\n  if (!headOrBody) {\n    throw new Error('Expected document.body not to be null. Stripe.js requires a <body> element.');\n  }\n\n  headOrBody.appendChild(script);\n  return script;\n};\n\nvar registerWrapper = function registerWrapper(stripe, startTime) {\n  if (!stripe || !stripe._registerWrapper) {\n    return;\n  }\n\n  stripe._registerWrapper({\n    name: 'stripe-js',\n    version: \"7.8.0\",\n    startTime: startTime\n  });\n};\n\nvar stripePromise$1 = null;\nvar onErrorListener = null;\nvar onLoadListener = null;\n\nvar onError = function onError(reject) {\n  return function (cause) {\n    reject(new Error('Failed to load Stripe.js', {\n      cause: cause\n    }));\n  };\n};\n\nvar onLoad = function onLoad(resolve, reject) {\n  return function () {\n    if (window.Stripe) {\n      resolve(window.Stripe);\n    } else {\n      reject(new Error('Stripe.js not available'));\n    }\n  };\n};\n\nvar loadScript = function loadScript(params) {\n  // Ensure that we only attempt to load Stripe.js at most once\n  if (stripePromise$1 !== null) {\n    return stripePromise$1;\n  }\n\n  stripePromise$1 = new Promise(function (resolve, reject) {\n    if (typeof window === 'undefined' || typeof document === 'undefined') {\n      // Resolve to null when imported server side. This makes the module\n      // safe to import in an isomorphic code base.\n      resolve(null);\n      return;\n    }\n\n    if (window.Stripe && params) {\n      console.warn(EXISTING_SCRIPT_MESSAGE);\n    }\n\n    if (window.Stripe) {\n      resolve(window.Stripe);\n      return;\n    }\n\n    try {\n      var script = findScript();\n\n      if (script && params) {\n        console.warn(EXISTING_SCRIPT_MESSAGE);\n      } else if (!script) {\n        script = injectScript(params);\n      } else if (script && onLoadListener !== null && onErrorListener !== null) {\n        var _script$parentNode;\n\n        // remove event listeners\n        script.removeEventListener('load', onLoadListener);\n        script.removeEventListener('error', onErrorListener); // if script exists, but we are reloading due to an error,\n        // reload script to trigger 'load' event\n\n        (_script$parentNode = script.parentNode) === null || _script$parentNode === void 0 ? void 0 : _script$parentNode.removeChild(script);\n        script = injectScript(params);\n      }\n\n      onLoadListener = onLoad(resolve, reject);\n      onErrorListener = onError(reject);\n      script.addEventListener('load', onLoadListener);\n      script.addEventListener('error', onErrorListener);\n    } catch (error) {\n      reject(error);\n      return;\n    }\n  }); // Resets stripePromise on error\n\n  return stripePromise$1[\"catch\"](function (error) {\n    stripePromise$1 = null;\n    return Promise.reject(error);\n  });\n};\nvar initStripe = function initStripe(maybeStripe, args, startTime) {\n  if (maybeStripe === null) {\n    return null;\n  }\n\n  var pk = args[0];\n  var isTestKey = pk.match(/^pk_test/); // @ts-expect-error this is not publicly typed\n\n  var version = runtimeVersionToUrlVersion(maybeStripe.version);\n  var expectedVersion = RELEASE_TRAIN;\n\n  if (isTestKey && version !== expectedVersion) {\n    console.warn(\"Stripe.js@\".concat(version, \" was loaded on the page, but @stripe/stripe-js@\").concat(\"7.8.0\", \" expected Stripe.js@\").concat(expectedVersion, \". This may result in unexpected behavior. For more information, see https://docs.stripe.com/sdks/stripejs-versioning\"));\n  }\n\n  var stripe = maybeStripe.apply(undefined, args);\n  registerWrapper(stripe, startTime);\n  return stripe;\n}; // eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types\n\nvar stripePromise;\nvar loadCalled = false;\n\nvar getStripePromise = function getStripePromise() {\n  if (stripePromise) {\n    return stripePromise;\n  }\n\n  stripePromise = loadScript(null)[\"catch\"](function (error) {\n    // clear cache on error\n    stripePromise = null;\n    return Promise.reject(error);\n  });\n  return stripePromise;\n}; // Execute our own script injection after a tick to give users time to do their\n// own script injection.\n\n\nPromise.resolve().then(function () {\n  return getStripePromise();\n})[\"catch\"](function (error) {\n  if (!loadCalled) {\n    console.warn(error);\n  }\n});\nvar loadStripe = function loadStripe() {\n  for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n    args[_key] = arguments[_key];\n  }\n\n  loadCalled = true;\n  var startTime = Date.now(); // if previous attempts are unsuccessful, will re-load script\n\n  return getStripePromise().then(function (maybeStripe) {\n    return initStripe(maybeStripe, args, startTime);\n  });\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@stripe/stripe-js/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@stripe/stripe-js/lib/index.mjs":
/*!******************************************************!*\
  !*** ./node_modules/@stripe/stripe-js/lib/index.mjs ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   loadStripe: () => (/* reexport safe */ _dist_index_mjs__WEBPACK_IMPORTED_MODULE_0__.loadStripe)\n/* harmony export */ });\n/* harmony import */ var _dist_index_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../dist/index.mjs */ \"(ssr)/./node_modules/@stripe/stripe-js/dist/index.mjs\");\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHN0cmlwZS9zdHJpcGUtanMvbGliL2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFrQyIsInNvdXJjZXMiOlsid2VicGFjazovL3F1b3RlLWFpLWNybS8uL25vZGVfbW9kdWxlcy9Ac3RyaXBlL3N0cmlwZS1qcy9saWIvaW5kZXgubWpzPzFhMDciXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0ICogZnJvbSAnLi4vZGlzdC9pbmRleC5tanMnO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@stripe/stripe-js/lib/index.mjs\n");

/***/ })

};
;