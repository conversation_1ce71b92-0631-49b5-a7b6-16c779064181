"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/whatwg-url";
exports.ids = ["vendor-chunks/whatwg-url"];
exports.modules = {

/***/ "(ssr)/./node_modules/whatwg-url/lib/URL-impl.js":
/*!*************************************************!*\
  !*** ./node_modules/whatwg-url/lib/URL-impl.js ***!
  \*************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nconst usm = __webpack_require__(/*! ./url-state-machine */ \"(ssr)/./node_modules/whatwg-url/lib/url-state-machine.js\");\n\nexports.implementation = class URLImpl {\n  constructor(constructorArgs) {\n    const url = constructorArgs[0];\n    const base = constructorArgs[1];\n\n    let parsedBase = null;\n    if (base !== undefined) {\n      parsedBase = usm.basicURLParse(base);\n      if (parsedBase === \"failure\") {\n        throw new TypeError(\"Invalid base URL\");\n      }\n    }\n\n    const parsedURL = usm.basicURLParse(url, { baseURL: parsedBase });\n    if (parsedURL === \"failure\") {\n      throw new TypeError(\"Invalid URL\");\n    }\n\n    this._url = parsedURL;\n\n    // TODO: query stuff\n  }\n\n  get href() {\n    return usm.serializeURL(this._url);\n  }\n\n  set href(v) {\n    const parsedURL = usm.basicURLParse(v);\n    if (parsedURL === \"failure\") {\n      throw new TypeError(\"Invalid URL\");\n    }\n\n    this._url = parsedURL;\n  }\n\n  get origin() {\n    return usm.serializeURLOrigin(this._url);\n  }\n\n  get protocol() {\n    return this._url.scheme + \":\";\n  }\n\n  set protocol(v) {\n    usm.basicURLParse(v + \":\", { url: this._url, stateOverride: \"scheme start\" });\n  }\n\n  get username() {\n    return this._url.username;\n  }\n\n  set username(v) {\n    if (usm.cannotHaveAUsernamePasswordPort(this._url)) {\n      return;\n    }\n\n    usm.setTheUsername(this._url, v);\n  }\n\n  get password() {\n    return this._url.password;\n  }\n\n  set password(v) {\n    if (usm.cannotHaveAUsernamePasswordPort(this._url)) {\n      return;\n    }\n\n    usm.setThePassword(this._url, v);\n  }\n\n  get host() {\n    const url = this._url;\n\n    if (url.host === null) {\n      return \"\";\n    }\n\n    if (url.port === null) {\n      return usm.serializeHost(url.host);\n    }\n\n    return usm.serializeHost(url.host) + \":\" + usm.serializeInteger(url.port);\n  }\n\n  set host(v) {\n    if (this._url.cannotBeABaseURL) {\n      return;\n    }\n\n    usm.basicURLParse(v, { url: this._url, stateOverride: \"host\" });\n  }\n\n  get hostname() {\n    if (this._url.host === null) {\n      return \"\";\n    }\n\n    return usm.serializeHost(this._url.host);\n  }\n\n  set hostname(v) {\n    if (this._url.cannotBeABaseURL) {\n      return;\n    }\n\n    usm.basicURLParse(v, { url: this._url, stateOverride: \"hostname\" });\n  }\n\n  get port() {\n    if (this._url.port === null) {\n      return \"\";\n    }\n\n    return usm.serializeInteger(this._url.port);\n  }\n\n  set port(v) {\n    if (usm.cannotHaveAUsernamePasswordPort(this._url)) {\n      return;\n    }\n\n    if (v === \"\") {\n      this._url.port = null;\n    } else {\n      usm.basicURLParse(v, { url: this._url, stateOverride: \"port\" });\n    }\n  }\n\n  get pathname() {\n    if (this._url.cannotBeABaseURL) {\n      return this._url.path[0];\n    }\n\n    if (this._url.path.length === 0) {\n      return \"\";\n    }\n\n    return \"/\" + this._url.path.join(\"/\");\n  }\n\n  set pathname(v) {\n    if (this._url.cannotBeABaseURL) {\n      return;\n    }\n\n    this._url.path = [];\n    usm.basicURLParse(v, { url: this._url, stateOverride: \"path start\" });\n  }\n\n  get search() {\n    if (this._url.query === null || this._url.query === \"\") {\n      return \"\";\n    }\n\n    return \"?\" + this._url.query;\n  }\n\n  set search(v) {\n    // TODO: query stuff\n\n    const url = this._url;\n\n    if (v === \"\") {\n      url.query = null;\n      return;\n    }\n\n    const input = v[0] === \"?\" ? v.substring(1) : v;\n    url.query = \"\";\n    usm.basicURLParse(input, { url, stateOverride: \"query\" });\n  }\n\n  get hash() {\n    if (this._url.fragment === null || this._url.fragment === \"\") {\n      return \"\";\n    }\n\n    return \"#\" + this._url.fragment;\n  }\n\n  set hash(v) {\n    if (v === \"\") {\n      this._url.fragment = null;\n      return;\n    }\n\n    const input = v[0] === \"#\" ? v.substring(1) : v;\n    this._url.fragment = \"\";\n    usm.basicURLParse(input, { url: this._url, stateOverride: \"fragment\" });\n  }\n\n  toJSON() {\n    return this.href;\n  }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/whatwg-url/lib/URL-impl.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/whatwg-url/lib/URL.js":
/*!********************************************!*\
  !*** ./node_modules/whatwg-url/lib/URL.js ***!
  \********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst conversions = __webpack_require__(/*! webidl-conversions */ \"(ssr)/./node_modules/webidl-conversions/lib/index.js\");\nconst utils = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/whatwg-url/lib/utils.js\");\nconst Impl = __webpack_require__(/*! .//URL-impl.js */ \"(ssr)/./node_modules/whatwg-url/lib/URL-impl.js\");\n\nconst impl = utils.implSymbol;\n\nfunction URL(url) {\n  if (!this || this[impl] || !(this instanceof URL)) {\n    throw new TypeError(\"Failed to construct 'URL': Please use the 'new' operator, this DOM object constructor cannot be called as a function.\");\n  }\n  if (arguments.length < 1) {\n    throw new TypeError(\"Failed to construct 'URL': 1 argument required, but only \" + arguments.length + \" present.\");\n  }\n  const args = [];\n  for (let i = 0; i < arguments.length && i < 2; ++i) {\n    args[i] = arguments[i];\n  }\n  args[0] = conversions[\"USVString\"](args[0]);\n  if (args[1] !== undefined) {\n  args[1] = conversions[\"USVString\"](args[1]);\n  }\n\n  module.exports.setup(this, args);\n}\n\nURL.prototype.toJSON = function toJSON() {\n  if (!this || !module.exports.is(this)) {\n    throw new TypeError(\"Illegal invocation\");\n  }\n  const args = [];\n  for (let i = 0; i < arguments.length && i < 0; ++i) {\n    args[i] = arguments[i];\n  }\n  return this[impl].toJSON.apply(this[impl], args);\n};\nObject.defineProperty(URL.prototype, \"href\", {\n  get() {\n    return this[impl].href;\n  },\n  set(V) {\n    V = conversions[\"USVString\"](V);\n    this[impl].href = V;\n  },\n  enumerable: true,\n  configurable: true\n});\n\nURL.prototype.toString = function () {\n  if (!this || !module.exports.is(this)) {\n    throw new TypeError(\"Illegal invocation\");\n  }\n  return this.href;\n};\n\nObject.defineProperty(URL.prototype, \"origin\", {\n  get() {\n    return this[impl].origin;\n  },\n  enumerable: true,\n  configurable: true\n});\n\nObject.defineProperty(URL.prototype, \"protocol\", {\n  get() {\n    return this[impl].protocol;\n  },\n  set(V) {\n    V = conversions[\"USVString\"](V);\n    this[impl].protocol = V;\n  },\n  enumerable: true,\n  configurable: true\n});\n\nObject.defineProperty(URL.prototype, \"username\", {\n  get() {\n    return this[impl].username;\n  },\n  set(V) {\n    V = conversions[\"USVString\"](V);\n    this[impl].username = V;\n  },\n  enumerable: true,\n  configurable: true\n});\n\nObject.defineProperty(URL.prototype, \"password\", {\n  get() {\n    return this[impl].password;\n  },\n  set(V) {\n    V = conversions[\"USVString\"](V);\n    this[impl].password = V;\n  },\n  enumerable: true,\n  configurable: true\n});\n\nObject.defineProperty(URL.prototype, \"host\", {\n  get() {\n    return this[impl].host;\n  },\n  set(V) {\n    V = conversions[\"USVString\"](V);\n    this[impl].host = V;\n  },\n  enumerable: true,\n  configurable: true\n});\n\nObject.defineProperty(URL.prototype, \"hostname\", {\n  get() {\n    return this[impl].hostname;\n  },\n  set(V) {\n    V = conversions[\"USVString\"](V);\n    this[impl].hostname = V;\n  },\n  enumerable: true,\n  configurable: true\n});\n\nObject.defineProperty(URL.prototype, \"port\", {\n  get() {\n    return this[impl].port;\n  },\n  set(V) {\n    V = conversions[\"USVString\"](V);\n    this[impl].port = V;\n  },\n  enumerable: true,\n  configurable: true\n});\n\nObject.defineProperty(URL.prototype, \"pathname\", {\n  get() {\n    return this[impl].pathname;\n  },\n  set(V) {\n    V = conversions[\"USVString\"](V);\n    this[impl].pathname = V;\n  },\n  enumerable: true,\n  configurable: true\n});\n\nObject.defineProperty(URL.prototype, \"search\", {\n  get() {\n    return this[impl].search;\n  },\n  set(V) {\n    V = conversions[\"USVString\"](V);\n    this[impl].search = V;\n  },\n  enumerable: true,\n  configurable: true\n});\n\nObject.defineProperty(URL.prototype, \"hash\", {\n  get() {\n    return this[impl].hash;\n  },\n  set(V) {\n    V = conversions[\"USVString\"](V);\n    this[impl].hash = V;\n  },\n  enumerable: true,\n  configurable: true\n});\n\n\nmodule.exports = {\n  is(obj) {\n    return !!obj && obj[impl] instanceof Impl.implementation;\n  },\n  create(constructorArgs, privateData) {\n    let obj = Object.create(URL.prototype);\n    this.setup(obj, constructorArgs, privateData);\n    return obj;\n  },\n  setup(obj, constructorArgs, privateData) {\n    if (!privateData) privateData = {};\n    privateData.wrapper = obj;\n\n    obj[impl] = new Impl.implementation(constructorArgs, privateData);\n    obj[impl][utils.wrapperSymbol] = obj;\n  },\n  interface: URL,\n  expose: {\n    Window: { URL: URL },\n    Worker: { URL: URL }\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/whatwg-url/lib/URL.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/whatwg-url/lib/public-api.js":
/*!***************************************************!*\
  !*** ./node_modules/whatwg-url/lib/public-api.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nexports.URL = __webpack_require__(/*! ./URL */ \"(ssr)/./node_modules/whatwg-url/lib/URL.js\")[\"interface\"];\nexports.serializeURL = __webpack_require__(/*! ./url-state-machine */ \"(ssr)/./node_modules/whatwg-url/lib/url-state-machine.js\").serializeURL;\nexports.serializeURLOrigin = __webpack_require__(/*! ./url-state-machine */ \"(ssr)/./node_modules/whatwg-url/lib/url-state-machine.js\").serializeURLOrigin;\nexports.basicURLParse = __webpack_require__(/*! ./url-state-machine */ \"(ssr)/./node_modules/whatwg-url/lib/url-state-machine.js\").basicURLParse;\nexports.setTheUsername = __webpack_require__(/*! ./url-state-machine */ \"(ssr)/./node_modules/whatwg-url/lib/url-state-machine.js\").setTheUsername;\nexports.setThePassword = __webpack_require__(/*! ./url-state-machine */ \"(ssr)/./node_modules/whatwg-url/lib/url-state-machine.js\").setThePassword;\nexports.serializeHost = __webpack_require__(/*! ./url-state-machine */ \"(ssr)/./node_modules/whatwg-url/lib/url-state-machine.js\").serializeHost;\nexports.serializeInteger = __webpack_require__(/*! ./url-state-machine */ \"(ssr)/./node_modules/whatwg-url/lib/url-state-machine.js\").serializeInteger;\nexports.parseURL = __webpack_require__(/*! ./url-state-machine */ \"(ssr)/./node_modules/whatwg-url/lib/url-state-machine.js\").parseURL;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvd2hhdHdnLXVybC9saWIvcHVibGljLWFwaS5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYix5R0FBd0M7QUFDeEMsOElBQWtFO0FBQ2xFLDBKQUE4RTtBQUM5RSxnSkFBb0U7QUFDcEUsa0pBQXNFO0FBQ3RFLGtKQUFzRTtBQUN0RSxnSkFBb0U7QUFDcEUsc0pBQTBFO0FBQzFFLHNJQUEwRCIsInNvdXJjZXMiOlsid2VicGFjazovL3F1b3RlLWFpLWNybS8uL25vZGVfbW9kdWxlcy93aGF0d2ctdXJsL2xpYi9wdWJsaWMtYXBpLmpzPzJmNWYiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5cbmV4cG9ydHMuVVJMID0gcmVxdWlyZShcIi4vVVJMXCIpLmludGVyZmFjZTtcbmV4cG9ydHMuc2VyaWFsaXplVVJMID0gcmVxdWlyZShcIi4vdXJsLXN0YXRlLW1hY2hpbmVcIikuc2VyaWFsaXplVVJMO1xuZXhwb3J0cy5zZXJpYWxpemVVUkxPcmlnaW4gPSByZXF1aXJlKFwiLi91cmwtc3RhdGUtbWFjaGluZVwiKS5zZXJpYWxpemVVUkxPcmlnaW47XG5leHBvcnRzLmJhc2ljVVJMUGFyc2UgPSByZXF1aXJlKFwiLi91cmwtc3RhdGUtbWFjaGluZVwiKS5iYXNpY1VSTFBhcnNlO1xuZXhwb3J0cy5zZXRUaGVVc2VybmFtZSA9IHJlcXVpcmUoXCIuL3VybC1zdGF0ZS1tYWNoaW5lXCIpLnNldFRoZVVzZXJuYW1lO1xuZXhwb3J0cy5zZXRUaGVQYXNzd29yZCA9IHJlcXVpcmUoXCIuL3VybC1zdGF0ZS1tYWNoaW5lXCIpLnNldFRoZVBhc3N3b3JkO1xuZXhwb3J0cy5zZXJpYWxpemVIb3N0ID0gcmVxdWlyZShcIi4vdXJsLXN0YXRlLW1hY2hpbmVcIikuc2VyaWFsaXplSG9zdDtcbmV4cG9ydHMuc2VyaWFsaXplSW50ZWdlciA9IHJlcXVpcmUoXCIuL3VybC1zdGF0ZS1tYWNoaW5lXCIpLnNlcmlhbGl6ZUludGVnZXI7XG5leHBvcnRzLnBhcnNlVVJMID0gcmVxdWlyZShcIi4vdXJsLXN0YXRlLW1hY2hpbmVcIikucGFyc2VVUkw7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/whatwg-url/lib/public-api.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/whatwg-url/lib/url-state-machine.js":
/*!**********************************************************!*\
  !*** ./node_modules/whatwg-url/lib/url-state-machine.js ***!
  \**********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\r\nconst punycode = __webpack_require__(/*! punycode */ \"punycode\");\r\nconst tr46 = __webpack_require__(/*! tr46 */ \"(ssr)/./node_modules/tr46/index.js\");\r\n\r\nconst specialSchemes = {\r\n  ftp: 21,\r\n  file: null,\r\n  gopher: 70,\r\n  http: 80,\r\n  https: 443,\r\n  ws: 80,\r\n  wss: 443\r\n};\r\n\r\nconst failure = Symbol(\"failure\");\r\n\r\nfunction countSymbols(str) {\r\n  return punycode.ucs2.decode(str).length;\r\n}\r\n\r\nfunction at(input, idx) {\r\n  const c = input[idx];\r\n  return isNaN(c) ? undefined : String.fromCodePoint(c);\r\n}\r\n\r\nfunction isASCIIDigit(c) {\r\n  return c >= 0x30 && c <= 0x39;\r\n}\r\n\r\nfunction isASCIIAlpha(c) {\r\n  return (c >= 0x41 && c <= 0x5A) || (c >= 0x61 && c <= 0x7A);\r\n}\r\n\r\nfunction isASCIIAlphanumeric(c) {\r\n  return isASCIIAlpha(c) || isASCIIDigit(c);\r\n}\r\n\r\nfunction isASCIIHex(c) {\r\n  return isASCIIDigit(c) || (c >= 0x41 && c <= 0x46) || (c >= 0x61 && c <= 0x66);\r\n}\r\n\r\nfunction isSingleDot(buffer) {\r\n  return buffer === \".\" || buffer.toLowerCase() === \"%2e\";\r\n}\r\n\r\nfunction isDoubleDot(buffer) {\r\n  buffer = buffer.toLowerCase();\r\n  return buffer === \"..\" || buffer === \"%2e.\" || buffer === \".%2e\" || buffer === \"%2e%2e\";\r\n}\r\n\r\nfunction isWindowsDriveLetterCodePoints(cp1, cp2) {\r\n  return isASCIIAlpha(cp1) && (cp2 === 58 || cp2 === 124);\r\n}\r\n\r\nfunction isWindowsDriveLetterString(string) {\r\n  return string.length === 2 && isASCIIAlpha(string.codePointAt(0)) && (string[1] === \":\" || string[1] === \"|\");\r\n}\r\n\r\nfunction isNormalizedWindowsDriveLetterString(string) {\r\n  return string.length === 2 && isASCIIAlpha(string.codePointAt(0)) && string[1] === \":\";\r\n}\r\n\r\nfunction containsForbiddenHostCodePoint(string) {\r\n  return string.search(/\\u0000|\\u0009|\\u000A|\\u000D|\\u0020|#|%|\\/|:|\\?|@|\\[|\\\\|\\]/) !== -1;\r\n}\r\n\r\nfunction containsForbiddenHostCodePointExcludingPercent(string) {\r\n  return string.search(/\\u0000|\\u0009|\\u000A|\\u000D|\\u0020|#|\\/|:|\\?|@|\\[|\\\\|\\]/) !== -1;\r\n}\r\n\r\nfunction isSpecialScheme(scheme) {\r\n  return specialSchemes[scheme] !== undefined;\r\n}\r\n\r\nfunction isSpecial(url) {\r\n  return isSpecialScheme(url.scheme);\r\n}\r\n\r\nfunction defaultPort(scheme) {\r\n  return specialSchemes[scheme];\r\n}\r\n\r\nfunction percentEncode(c) {\r\n  let hex = c.toString(16).toUpperCase();\r\n  if (hex.length === 1) {\r\n    hex = \"0\" + hex;\r\n  }\r\n\r\n  return \"%\" + hex;\r\n}\r\n\r\nfunction utf8PercentEncode(c) {\r\n  const buf = new Buffer(c);\r\n\r\n  let str = \"\";\r\n\r\n  for (let i = 0; i < buf.length; ++i) {\r\n    str += percentEncode(buf[i]);\r\n  }\r\n\r\n  return str;\r\n}\r\n\r\nfunction utf8PercentDecode(str) {\r\n  const input = new Buffer(str);\r\n  const output = [];\r\n  for (let i = 0; i < input.length; ++i) {\r\n    if (input[i] !== 37) {\r\n      output.push(input[i]);\r\n    } else if (input[i] === 37 && isASCIIHex(input[i + 1]) && isASCIIHex(input[i + 2])) {\r\n      output.push(parseInt(input.slice(i + 1, i + 3).toString(), 16));\r\n      i += 2;\r\n    } else {\r\n      output.push(input[i]);\r\n    }\r\n  }\r\n  return new Buffer(output).toString();\r\n}\r\n\r\nfunction isC0ControlPercentEncode(c) {\r\n  return c <= 0x1F || c > 0x7E;\r\n}\r\n\r\nconst extraPathPercentEncodeSet = new Set([32, 34, 35, 60, 62, 63, 96, 123, 125]);\r\nfunction isPathPercentEncode(c) {\r\n  return isC0ControlPercentEncode(c) || extraPathPercentEncodeSet.has(c);\r\n}\r\n\r\nconst extraUserinfoPercentEncodeSet =\r\n  new Set([47, 58, 59, 61, 64, 91, 92, 93, 94, 124]);\r\nfunction isUserinfoPercentEncode(c) {\r\n  return isPathPercentEncode(c) || extraUserinfoPercentEncodeSet.has(c);\r\n}\r\n\r\nfunction percentEncodeChar(c, encodeSetPredicate) {\r\n  const cStr = String.fromCodePoint(c);\r\n\r\n  if (encodeSetPredicate(c)) {\r\n    return utf8PercentEncode(cStr);\r\n  }\r\n\r\n  return cStr;\r\n}\r\n\r\nfunction parseIPv4Number(input) {\r\n  let R = 10;\r\n\r\n  if (input.length >= 2 && input.charAt(0) === \"0\" && input.charAt(1).toLowerCase() === \"x\") {\r\n    input = input.substring(2);\r\n    R = 16;\r\n  } else if (input.length >= 2 && input.charAt(0) === \"0\") {\r\n    input = input.substring(1);\r\n    R = 8;\r\n  }\r\n\r\n  if (input === \"\") {\r\n    return 0;\r\n  }\r\n\r\n  const regex = R === 10 ? /[^0-9]/ : (R === 16 ? /[^0-9A-Fa-f]/ : /[^0-7]/);\r\n  if (regex.test(input)) {\r\n    return failure;\r\n  }\r\n\r\n  return parseInt(input, R);\r\n}\r\n\r\nfunction parseIPv4(input) {\r\n  const parts = input.split(\".\");\r\n  if (parts[parts.length - 1] === \"\") {\r\n    if (parts.length > 1) {\r\n      parts.pop();\r\n    }\r\n  }\r\n\r\n  if (parts.length > 4) {\r\n    return input;\r\n  }\r\n\r\n  const numbers = [];\r\n  for (const part of parts) {\r\n    if (part === \"\") {\r\n      return input;\r\n    }\r\n    const n = parseIPv4Number(part);\r\n    if (n === failure) {\r\n      return input;\r\n    }\r\n\r\n    numbers.push(n);\r\n  }\r\n\r\n  for (let i = 0; i < numbers.length - 1; ++i) {\r\n    if (numbers[i] > 255) {\r\n      return failure;\r\n    }\r\n  }\r\n  if (numbers[numbers.length - 1] >= Math.pow(256, 5 - numbers.length)) {\r\n    return failure;\r\n  }\r\n\r\n  let ipv4 = numbers.pop();\r\n  let counter = 0;\r\n\r\n  for (const n of numbers) {\r\n    ipv4 += n * Math.pow(256, 3 - counter);\r\n    ++counter;\r\n  }\r\n\r\n  return ipv4;\r\n}\r\n\r\nfunction serializeIPv4(address) {\r\n  let output = \"\";\r\n  let n = address;\r\n\r\n  for (let i = 1; i <= 4; ++i) {\r\n    output = String(n % 256) + output;\r\n    if (i !== 4) {\r\n      output = \".\" + output;\r\n    }\r\n    n = Math.floor(n / 256);\r\n  }\r\n\r\n  return output;\r\n}\r\n\r\nfunction parseIPv6(input) {\r\n  const address = [0, 0, 0, 0, 0, 0, 0, 0];\r\n  let pieceIndex = 0;\r\n  let compress = null;\r\n  let pointer = 0;\r\n\r\n  input = punycode.ucs2.decode(input);\r\n\r\n  if (input[pointer] === 58) {\r\n    if (input[pointer + 1] !== 58) {\r\n      return failure;\r\n    }\r\n\r\n    pointer += 2;\r\n    ++pieceIndex;\r\n    compress = pieceIndex;\r\n  }\r\n\r\n  while (pointer < input.length) {\r\n    if (pieceIndex === 8) {\r\n      return failure;\r\n    }\r\n\r\n    if (input[pointer] === 58) {\r\n      if (compress !== null) {\r\n        return failure;\r\n      }\r\n      ++pointer;\r\n      ++pieceIndex;\r\n      compress = pieceIndex;\r\n      continue;\r\n    }\r\n\r\n    let value = 0;\r\n    let length = 0;\r\n\r\n    while (length < 4 && isASCIIHex(input[pointer])) {\r\n      value = value * 0x10 + parseInt(at(input, pointer), 16);\r\n      ++pointer;\r\n      ++length;\r\n    }\r\n\r\n    if (input[pointer] === 46) {\r\n      if (length === 0) {\r\n        return failure;\r\n      }\r\n\r\n      pointer -= length;\r\n\r\n      if (pieceIndex > 6) {\r\n        return failure;\r\n      }\r\n\r\n      let numbersSeen = 0;\r\n\r\n      while (input[pointer] !== undefined) {\r\n        let ipv4Piece = null;\r\n\r\n        if (numbersSeen > 0) {\r\n          if (input[pointer] === 46 && numbersSeen < 4) {\r\n            ++pointer;\r\n          } else {\r\n            return failure;\r\n          }\r\n        }\r\n\r\n        if (!isASCIIDigit(input[pointer])) {\r\n          return failure;\r\n        }\r\n\r\n        while (isASCIIDigit(input[pointer])) {\r\n          const number = parseInt(at(input, pointer));\r\n          if (ipv4Piece === null) {\r\n            ipv4Piece = number;\r\n          } else if (ipv4Piece === 0) {\r\n            return failure;\r\n          } else {\r\n            ipv4Piece = ipv4Piece * 10 + number;\r\n          }\r\n          if (ipv4Piece > 255) {\r\n            return failure;\r\n          }\r\n          ++pointer;\r\n        }\r\n\r\n        address[pieceIndex] = address[pieceIndex] * 0x100 + ipv4Piece;\r\n\r\n        ++numbersSeen;\r\n\r\n        if (numbersSeen === 2 || numbersSeen === 4) {\r\n          ++pieceIndex;\r\n        }\r\n      }\r\n\r\n      if (numbersSeen !== 4) {\r\n        return failure;\r\n      }\r\n\r\n      break;\r\n    } else if (input[pointer] === 58) {\r\n      ++pointer;\r\n      if (input[pointer] === undefined) {\r\n        return failure;\r\n      }\r\n    } else if (input[pointer] !== undefined) {\r\n      return failure;\r\n    }\r\n\r\n    address[pieceIndex] = value;\r\n    ++pieceIndex;\r\n  }\r\n\r\n  if (compress !== null) {\r\n    let swaps = pieceIndex - compress;\r\n    pieceIndex = 7;\r\n    while (pieceIndex !== 0 && swaps > 0) {\r\n      const temp = address[compress + swaps - 1];\r\n      address[compress + swaps - 1] = address[pieceIndex];\r\n      address[pieceIndex] = temp;\r\n      --pieceIndex;\r\n      --swaps;\r\n    }\r\n  } else if (compress === null && pieceIndex !== 8) {\r\n    return failure;\r\n  }\r\n\r\n  return address;\r\n}\r\n\r\nfunction serializeIPv6(address) {\r\n  let output = \"\";\r\n  const seqResult = findLongestZeroSequence(address);\r\n  const compress = seqResult.idx;\r\n  let ignore0 = false;\r\n\r\n  for (let pieceIndex = 0; pieceIndex <= 7; ++pieceIndex) {\r\n    if (ignore0 && address[pieceIndex] === 0) {\r\n      continue;\r\n    } else if (ignore0) {\r\n      ignore0 = false;\r\n    }\r\n\r\n    if (compress === pieceIndex) {\r\n      const separator = pieceIndex === 0 ? \"::\" : \":\";\r\n      output += separator;\r\n      ignore0 = true;\r\n      continue;\r\n    }\r\n\r\n    output += address[pieceIndex].toString(16);\r\n\r\n    if (pieceIndex !== 7) {\r\n      output += \":\";\r\n    }\r\n  }\r\n\r\n  return output;\r\n}\r\n\r\nfunction parseHost(input, isSpecialArg) {\r\n  if (input[0] === \"[\") {\r\n    if (input[input.length - 1] !== \"]\") {\r\n      return failure;\r\n    }\r\n\r\n    return parseIPv6(input.substring(1, input.length - 1));\r\n  }\r\n\r\n  if (!isSpecialArg) {\r\n    return parseOpaqueHost(input);\r\n  }\r\n\r\n  const domain = utf8PercentDecode(input);\r\n  const asciiDomain = tr46.toASCII(domain, false, tr46.PROCESSING_OPTIONS.NONTRANSITIONAL, false);\r\n  if (asciiDomain === null) {\r\n    return failure;\r\n  }\r\n\r\n  if (containsForbiddenHostCodePoint(asciiDomain)) {\r\n    return failure;\r\n  }\r\n\r\n  const ipv4Host = parseIPv4(asciiDomain);\r\n  if (typeof ipv4Host === \"number\" || ipv4Host === failure) {\r\n    return ipv4Host;\r\n  }\r\n\r\n  return asciiDomain;\r\n}\r\n\r\nfunction parseOpaqueHost(input) {\r\n  if (containsForbiddenHostCodePointExcludingPercent(input)) {\r\n    return failure;\r\n  }\r\n\r\n  let output = \"\";\r\n  const decoded = punycode.ucs2.decode(input);\r\n  for (let i = 0; i < decoded.length; ++i) {\r\n    output += percentEncodeChar(decoded[i], isC0ControlPercentEncode);\r\n  }\r\n  return output;\r\n}\r\n\r\nfunction findLongestZeroSequence(arr) {\r\n  let maxIdx = null;\r\n  let maxLen = 1; // only find elements > 1\r\n  let currStart = null;\r\n  let currLen = 0;\r\n\r\n  for (let i = 0; i < arr.length; ++i) {\r\n    if (arr[i] !== 0) {\r\n      if (currLen > maxLen) {\r\n        maxIdx = currStart;\r\n        maxLen = currLen;\r\n      }\r\n\r\n      currStart = null;\r\n      currLen = 0;\r\n    } else {\r\n      if (currStart === null) {\r\n        currStart = i;\r\n      }\r\n      ++currLen;\r\n    }\r\n  }\r\n\r\n  // if trailing zeros\r\n  if (currLen > maxLen) {\r\n    maxIdx = currStart;\r\n    maxLen = currLen;\r\n  }\r\n\r\n  return {\r\n    idx: maxIdx,\r\n    len: maxLen\r\n  };\r\n}\r\n\r\nfunction serializeHost(host) {\r\n  if (typeof host === \"number\") {\r\n    return serializeIPv4(host);\r\n  }\r\n\r\n  // IPv6 serializer\r\n  if (host instanceof Array) {\r\n    return \"[\" + serializeIPv6(host) + \"]\";\r\n  }\r\n\r\n  return host;\r\n}\r\n\r\nfunction trimControlChars(url) {\r\n  return url.replace(/^[\\u0000-\\u001F\\u0020]+|[\\u0000-\\u001F\\u0020]+$/g, \"\");\r\n}\r\n\r\nfunction trimTabAndNewline(url) {\r\n  return url.replace(/\\u0009|\\u000A|\\u000D/g, \"\");\r\n}\r\n\r\nfunction shortenPath(url) {\r\n  const path = url.path;\r\n  if (path.length === 0) {\r\n    return;\r\n  }\r\n  if (url.scheme === \"file\" && path.length === 1 && isNormalizedWindowsDriveLetter(path[0])) {\r\n    return;\r\n  }\r\n\r\n  path.pop();\r\n}\r\n\r\nfunction includesCredentials(url) {\r\n  return url.username !== \"\" || url.password !== \"\";\r\n}\r\n\r\nfunction cannotHaveAUsernamePasswordPort(url) {\r\n  return url.host === null || url.host === \"\" || url.cannotBeABaseURL || url.scheme === \"file\";\r\n}\r\n\r\nfunction isNormalizedWindowsDriveLetter(string) {\r\n  return /^[A-Za-z]:$/.test(string);\r\n}\r\n\r\nfunction URLStateMachine(input, base, encodingOverride, url, stateOverride) {\r\n  this.pointer = 0;\r\n  this.input = input;\r\n  this.base = base || null;\r\n  this.encodingOverride = encodingOverride || \"utf-8\";\r\n  this.stateOverride = stateOverride;\r\n  this.url = url;\r\n  this.failure = false;\r\n  this.parseError = false;\r\n\r\n  if (!this.url) {\r\n    this.url = {\r\n      scheme: \"\",\r\n      username: \"\",\r\n      password: \"\",\r\n      host: null,\r\n      port: null,\r\n      path: [],\r\n      query: null,\r\n      fragment: null,\r\n\r\n      cannotBeABaseURL: false\r\n    };\r\n\r\n    const res = trimControlChars(this.input);\r\n    if (res !== this.input) {\r\n      this.parseError = true;\r\n    }\r\n    this.input = res;\r\n  }\r\n\r\n  const res = trimTabAndNewline(this.input);\r\n  if (res !== this.input) {\r\n    this.parseError = true;\r\n  }\r\n  this.input = res;\r\n\r\n  this.state = stateOverride || \"scheme start\";\r\n\r\n  this.buffer = \"\";\r\n  this.atFlag = false;\r\n  this.arrFlag = false;\r\n  this.passwordTokenSeenFlag = false;\r\n\r\n  this.input = punycode.ucs2.decode(this.input);\r\n\r\n  for (; this.pointer <= this.input.length; ++this.pointer) {\r\n    const c = this.input[this.pointer];\r\n    const cStr = isNaN(c) ? undefined : String.fromCodePoint(c);\r\n\r\n    // exec state machine\r\n    const ret = this[\"parse \" + this.state](c, cStr);\r\n    if (!ret) {\r\n      break; // terminate algorithm\r\n    } else if (ret === failure) {\r\n      this.failure = true;\r\n      break;\r\n    }\r\n  }\r\n}\r\n\r\nURLStateMachine.prototype[\"parse scheme start\"] = function parseSchemeStart(c, cStr) {\r\n  if (isASCIIAlpha(c)) {\r\n    this.buffer += cStr.toLowerCase();\r\n    this.state = \"scheme\";\r\n  } else if (!this.stateOverride) {\r\n    this.state = \"no scheme\";\r\n    --this.pointer;\r\n  } else {\r\n    this.parseError = true;\r\n    return failure;\r\n  }\r\n\r\n  return true;\r\n};\r\n\r\nURLStateMachine.prototype[\"parse scheme\"] = function parseScheme(c, cStr) {\r\n  if (isASCIIAlphanumeric(c) || c === 43 || c === 45 || c === 46) {\r\n    this.buffer += cStr.toLowerCase();\r\n  } else if (c === 58) {\r\n    if (this.stateOverride) {\r\n      if (isSpecial(this.url) && !isSpecialScheme(this.buffer)) {\r\n        return false;\r\n      }\r\n\r\n      if (!isSpecial(this.url) && isSpecialScheme(this.buffer)) {\r\n        return false;\r\n      }\r\n\r\n      if ((includesCredentials(this.url) || this.url.port !== null) && this.buffer === \"file\") {\r\n        return false;\r\n      }\r\n\r\n      if (this.url.scheme === \"file\" && (this.url.host === \"\" || this.url.host === null)) {\r\n        return false;\r\n      }\r\n    }\r\n    this.url.scheme = this.buffer;\r\n    this.buffer = \"\";\r\n    if (this.stateOverride) {\r\n      return false;\r\n    }\r\n    if (this.url.scheme === \"file\") {\r\n      if (this.input[this.pointer + 1] !== 47 || this.input[this.pointer + 2] !== 47) {\r\n        this.parseError = true;\r\n      }\r\n      this.state = \"file\";\r\n    } else if (isSpecial(this.url) && this.base !== null && this.base.scheme === this.url.scheme) {\r\n      this.state = \"special relative or authority\";\r\n    } else if (isSpecial(this.url)) {\r\n      this.state = \"special authority slashes\";\r\n    } else if (this.input[this.pointer + 1] === 47) {\r\n      this.state = \"path or authority\";\r\n      ++this.pointer;\r\n    } else {\r\n      this.url.cannotBeABaseURL = true;\r\n      this.url.path.push(\"\");\r\n      this.state = \"cannot-be-a-base-URL path\";\r\n    }\r\n  } else if (!this.stateOverride) {\r\n    this.buffer = \"\";\r\n    this.state = \"no scheme\";\r\n    this.pointer = -1;\r\n  } else {\r\n    this.parseError = true;\r\n    return failure;\r\n  }\r\n\r\n  return true;\r\n};\r\n\r\nURLStateMachine.prototype[\"parse no scheme\"] = function parseNoScheme(c) {\r\n  if (this.base === null || (this.base.cannotBeABaseURL && c !== 35)) {\r\n    return failure;\r\n  } else if (this.base.cannotBeABaseURL && c === 35) {\r\n    this.url.scheme = this.base.scheme;\r\n    this.url.path = this.base.path.slice();\r\n    this.url.query = this.base.query;\r\n    this.url.fragment = \"\";\r\n    this.url.cannotBeABaseURL = true;\r\n    this.state = \"fragment\";\r\n  } else if (this.base.scheme === \"file\") {\r\n    this.state = \"file\";\r\n    --this.pointer;\r\n  } else {\r\n    this.state = \"relative\";\r\n    --this.pointer;\r\n  }\r\n\r\n  return true;\r\n};\r\n\r\nURLStateMachine.prototype[\"parse special relative or authority\"] = function parseSpecialRelativeOrAuthority(c) {\r\n  if (c === 47 && this.input[this.pointer + 1] === 47) {\r\n    this.state = \"special authority ignore slashes\";\r\n    ++this.pointer;\r\n  } else {\r\n    this.parseError = true;\r\n    this.state = \"relative\";\r\n    --this.pointer;\r\n  }\r\n\r\n  return true;\r\n};\r\n\r\nURLStateMachine.prototype[\"parse path or authority\"] = function parsePathOrAuthority(c) {\r\n  if (c === 47) {\r\n    this.state = \"authority\";\r\n  } else {\r\n    this.state = \"path\";\r\n    --this.pointer;\r\n  }\r\n\r\n  return true;\r\n};\r\n\r\nURLStateMachine.prototype[\"parse relative\"] = function parseRelative(c) {\r\n  this.url.scheme = this.base.scheme;\r\n  if (isNaN(c)) {\r\n    this.url.username = this.base.username;\r\n    this.url.password = this.base.password;\r\n    this.url.host = this.base.host;\r\n    this.url.port = this.base.port;\r\n    this.url.path = this.base.path.slice();\r\n    this.url.query = this.base.query;\r\n  } else if (c === 47) {\r\n    this.state = \"relative slash\";\r\n  } else if (c === 63) {\r\n    this.url.username = this.base.username;\r\n    this.url.password = this.base.password;\r\n    this.url.host = this.base.host;\r\n    this.url.port = this.base.port;\r\n    this.url.path = this.base.path.slice();\r\n    this.url.query = \"\";\r\n    this.state = \"query\";\r\n  } else if (c === 35) {\r\n    this.url.username = this.base.username;\r\n    this.url.password = this.base.password;\r\n    this.url.host = this.base.host;\r\n    this.url.port = this.base.port;\r\n    this.url.path = this.base.path.slice();\r\n    this.url.query = this.base.query;\r\n    this.url.fragment = \"\";\r\n    this.state = \"fragment\";\r\n  } else if (isSpecial(this.url) && c === 92) {\r\n    this.parseError = true;\r\n    this.state = \"relative slash\";\r\n  } else {\r\n    this.url.username = this.base.username;\r\n    this.url.password = this.base.password;\r\n    this.url.host = this.base.host;\r\n    this.url.port = this.base.port;\r\n    this.url.path = this.base.path.slice(0, this.base.path.length - 1);\r\n\r\n    this.state = \"path\";\r\n    --this.pointer;\r\n  }\r\n\r\n  return true;\r\n};\r\n\r\nURLStateMachine.prototype[\"parse relative slash\"] = function parseRelativeSlash(c) {\r\n  if (isSpecial(this.url) && (c === 47 || c === 92)) {\r\n    if (c === 92) {\r\n      this.parseError = true;\r\n    }\r\n    this.state = \"special authority ignore slashes\";\r\n  } else if (c === 47) {\r\n    this.state = \"authority\";\r\n  } else {\r\n    this.url.username = this.base.username;\r\n    this.url.password = this.base.password;\r\n    this.url.host = this.base.host;\r\n    this.url.port = this.base.port;\r\n    this.state = \"path\";\r\n    --this.pointer;\r\n  }\r\n\r\n  return true;\r\n};\r\n\r\nURLStateMachine.prototype[\"parse special authority slashes\"] = function parseSpecialAuthoritySlashes(c) {\r\n  if (c === 47 && this.input[this.pointer + 1] === 47) {\r\n    this.state = \"special authority ignore slashes\";\r\n    ++this.pointer;\r\n  } else {\r\n    this.parseError = true;\r\n    this.state = \"special authority ignore slashes\";\r\n    --this.pointer;\r\n  }\r\n\r\n  return true;\r\n};\r\n\r\nURLStateMachine.prototype[\"parse special authority ignore slashes\"] = function parseSpecialAuthorityIgnoreSlashes(c) {\r\n  if (c !== 47 && c !== 92) {\r\n    this.state = \"authority\";\r\n    --this.pointer;\r\n  } else {\r\n    this.parseError = true;\r\n  }\r\n\r\n  return true;\r\n};\r\n\r\nURLStateMachine.prototype[\"parse authority\"] = function parseAuthority(c, cStr) {\r\n  if (c === 64) {\r\n    this.parseError = true;\r\n    if (this.atFlag) {\r\n      this.buffer = \"%40\" + this.buffer;\r\n    }\r\n    this.atFlag = true;\r\n\r\n    // careful, this is based on buffer and has its own pointer (this.pointer != pointer) and inner chars\r\n    const len = countSymbols(this.buffer);\r\n    for (let pointer = 0; pointer < len; ++pointer) {\r\n      const codePoint = this.buffer.codePointAt(pointer);\r\n\r\n      if (codePoint === 58 && !this.passwordTokenSeenFlag) {\r\n        this.passwordTokenSeenFlag = true;\r\n        continue;\r\n      }\r\n      const encodedCodePoints = percentEncodeChar(codePoint, isUserinfoPercentEncode);\r\n      if (this.passwordTokenSeenFlag) {\r\n        this.url.password += encodedCodePoints;\r\n      } else {\r\n        this.url.username += encodedCodePoints;\r\n      }\r\n    }\r\n    this.buffer = \"\";\r\n  } else if (isNaN(c) || c === 47 || c === 63 || c === 35 ||\r\n             (isSpecial(this.url) && c === 92)) {\r\n    if (this.atFlag && this.buffer === \"\") {\r\n      this.parseError = true;\r\n      return failure;\r\n    }\r\n    this.pointer -= countSymbols(this.buffer) + 1;\r\n    this.buffer = \"\";\r\n    this.state = \"host\";\r\n  } else {\r\n    this.buffer += cStr;\r\n  }\r\n\r\n  return true;\r\n};\r\n\r\nURLStateMachine.prototype[\"parse hostname\"] =\r\nURLStateMachine.prototype[\"parse host\"] = function parseHostName(c, cStr) {\r\n  if (this.stateOverride && this.url.scheme === \"file\") {\r\n    --this.pointer;\r\n    this.state = \"file host\";\r\n  } else if (c === 58 && !this.arrFlag) {\r\n    if (this.buffer === \"\") {\r\n      this.parseError = true;\r\n      return failure;\r\n    }\r\n\r\n    const host = parseHost(this.buffer, isSpecial(this.url));\r\n    if (host === failure) {\r\n      return failure;\r\n    }\r\n\r\n    this.url.host = host;\r\n    this.buffer = \"\";\r\n    this.state = \"port\";\r\n    if (this.stateOverride === \"hostname\") {\r\n      return false;\r\n    }\r\n  } else if (isNaN(c) || c === 47 || c === 63 || c === 35 ||\r\n             (isSpecial(this.url) && c === 92)) {\r\n    --this.pointer;\r\n    if (isSpecial(this.url) && this.buffer === \"\") {\r\n      this.parseError = true;\r\n      return failure;\r\n    } else if (this.stateOverride && this.buffer === \"\" &&\r\n               (includesCredentials(this.url) || this.url.port !== null)) {\r\n      this.parseError = true;\r\n      return false;\r\n    }\r\n\r\n    const host = parseHost(this.buffer, isSpecial(this.url));\r\n    if (host === failure) {\r\n      return failure;\r\n    }\r\n\r\n    this.url.host = host;\r\n    this.buffer = \"\";\r\n    this.state = \"path start\";\r\n    if (this.stateOverride) {\r\n      return false;\r\n    }\r\n  } else {\r\n    if (c === 91) {\r\n      this.arrFlag = true;\r\n    } else if (c === 93) {\r\n      this.arrFlag = false;\r\n    }\r\n    this.buffer += cStr;\r\n  }\r\n\r\n  return true;\r\n};\r\n\r\nURLStateMachine.prototype[\"parse port\"] = function parsePort(c, cStr) {\r\n  if (isASCIIDigit(c)) {\r\n    this.buffer += cStr;\r\n  } else if (isNaN(c) || c === 47 || c === 63 || c === 35 ||\r\n             (isSpecial(this.url) && c === 92) ||\r\n             this.stateOverride) {\r\n    if (this.buffer !== \"\") {\r\n      const port = parseInt(this.buffer);\r\n      if (port > Math.pow(2, 16) - 1) {\r\n        this.parseError = true;\r\n        return failure;\r\n      }\r\n      this.url.port = port === defaultPort(this.url.scheme) ? null : port;\r\n      this.buffer = \"\";\r\n    }\r\n    if (this.stateOverride) {\r\n      return false;\r\n    }\r\n    this.state = \"path start\";\r\n    --this.pointer;\r\n  } else {\r\n    this.parseError = true;\r\n    return failure;\r\n  }\r\n\r\n  return true;\r\n};\r\n\r\nconst fileOtherwiseCodePoints = new Set([47, 92, 63, 35]);\r\n\r\nURLStateMachine.prototype[\"parse file\"] = function parseFile(c) {\r\n  this.url.scheme = \"file\";\r\n\r\n  if (c === 47 || c === 92) {\r\n    if (c === 92) {\r\n      this.parseError = true;\r\n    }\r\n    this.state = \"file slash\";\r\n  } else if (this.base !== null && this.base.scheme === \"file\") {\r\n    if (isNaN(c)) {\r\n      this.url.host = this.base.host;\r\n      this.url.path = this.base.path.slice();\r\n      this.url.query = this.base.query;\r\n    } else if (c === 63) {\r\n      this.url.host = this.base.host;\r\n      this.url.path = this.base.path.slice();\r\n      this.url.query = \"\";\r\n      this.state = \"query\";\r\n    } else if (c === 35) {\r\n      this.url.host = this.base.host;\r\n      this.url.path = this.base.path.slice();\r\n      this.url.query = this.base.query;\r\n      this.url.fragment = \"\";\r\n      this.state = \"fragment\";\r\n    } else {\r\n      if (this.input.length - this.pointer - 1 === 0 || // remaining consists of 0 code points\r\n          !isWindowsDriveLetterCodePoints(c, this.input[this.pointer + 1]) ||\r\n          (this.input.length - this.pointer - 1 >= 2 && // remaining has at least 2 code points\r\n           !fileOtherwiseCodePoints.has(this.input[this.pointer + 2]))) {\r\n        this.url.host = this.base.host;\r\n        this.url.path = this.base.path.slice();\r\n        shortenPath(this.url);\r\n      } else {\r\n        this.parseError = true;\r\n      }\r\n\r\n      this.state = \"path\";\r\n      --this.pointer;\r\n    }\r\n  } else {\r\n    this.state = \"path\";\r\n    --this.pointer;\r\n  }\r\n\r\n  return true;\r\n};\r\n\r\nURLStateMachine.prototype[\"parse file slash\"] = function parseFileSlash(c) {\r\n  if (c === 47 || c === 92) {\r\n    if (c === 92) {\r\n      this.parseError = true;\r\n    }\r\n    this.state = \"file host\";\r\n  } else {\r\n    if (this.base !== null && this.base.scheme === \"file\") {\r\n      if (isNormalizedWindowsDriveLetterString(this.base.path[0])) {\r\n        this.url.path.push(this.base.path[0]);\r\n      } else {\r\n        this.url.host = this.base.host;\r\n      }\r\n    }\r\n    this.state = \"path\";\r\n    --this.pointer;\r\n  }\r\n\r\n  return true;\r\n};\r\n\r\nURLStateMachine.prototype[\"parse file host\"] = function parseFileHost(c, cStr) {\r\n  if (isNaN(c) || c === 47 || c === 92 || c === 63 || c === 35) {\r\n    --this.pointer;\r\n    if (!this.stateOverride && isWindowsDriveLetterString(this.buffer)) {\r\n      this.parseError = true;\r\n      this.state = \"path\";\r\n    } else if (this.buffer === \"\") {\r\n      this.url.host = \"\";\r\n      if (this.stateOverride) {\r\n        return false;\r\n      }\r\n      this.state = \"path start\";\r\n    } else {\r\n      let host = parseHost(this.buffer, isSpecial(this.url));\r\n      if (host === failure) {\r\n        return failure;\r\n      }\r\n      if (host === \"localhost\") {\r\n        host = \"\";\r\n      }\r\n      this.url.host = host;\r\n\r\n      if (this.stateOverride) {\r\n        return false;\r\n      }\r\n\r\n      this.buffer = \"\";\r\n      this.state = \"path start\";\r\n    }\r\n  } else {\r\n    this.buffer += cStr;\r\n  }\r\n\r\n  return true;\r\n};\r\n\r\nURLStateMachine.prototype[\"parse path start\"] = function parsePathStart(c) {\r\n  if (isSpecial(this.url)) {\r\n    if (c === 92) {\r\n      this.parseError = true;\r\n    }\r\n    this.state = \"path\";\r\n\r\n    if (c !== 47 && c !== 92) {\r\n      --this.pointer;\r\n    }\r\n  } else if (!this.stateOverride && c === 63) {\r\n    this.url.query = \"\";\r\n    this.state = \"query\";\r\n  } else if (!this.stateOverride && c === 35) {\r\n    this.url.fragment = \"\";\r\n    this.state = \"fragment\";\r\n  } else if (c !== undefined) {\r\n    this.state = \"path\";\r\n    if (c !== 47) {\r\n      --this.pointer;\r\n    }\r\n  }\r\n\r\n  return true;\r\n};\r\n\r\nURLStateMachine.prototype[\"parse path\"] = function parsePath(c) {\r\n  if (isNaN(c) || c === 47 || (isSpecial(this.url) && c === 92) ||\r\n      (!this.stateOverride && (c === 63 || c === 35))) {\r\n    if (isSpecial(this.url) && c === 92) {\r\n      this.parseError = true;\r\n    }\r\n\r\n    if (isDoubleDot(this.buffer)) {\r\n      shortenPath(this.url);\r\n      if (c !== 47 && !(isSpecial(this.url) && c === 92)) {\r\n        this.url.path.push(\"\");\r\n      }\r\n    } else if (isSingleDot(this.buffer) && c !== 47 &&\r\n               !(isSpecial(this.url) && c === 92)) {\r\n      this.url.path.push(\"\");\r\n    } else if (!isSingleDot(this.buffer)) {\r\n      if (this.url.scheme === \"file\" && this.url.path.length === 0 && isWindowsDriveLetterString(this.buffer)) {\r\n        if (this.url.host !== \"\" && this.url.host !== null) {\r\n          this.parseError = true;\r\n          this.url.host = \"\";\r\n        }\r\n        this.buffer = this.buffer[0] + \":\";\r\n      }\r\n      this.url.path.push(this.buffer);\r\n    }\r\n    this.buffer = \"\";\r\n    if (this.url.scheme === \"file\" && (c === undefined || c === 63 || c === 35)) {\r\n      while (this.url.path.length > 1 && this.url.path[0] === \"\") {\r\n        this.parseError = true;\r\n        this.url.path.shift();\r\n      }\r\n    }\r\n    if (c === 63) {\r\n      this.url.query = \"\";\r\n      this.state = \"query\";\r\n    }\r\n    if (c === 35) {\r\n      this.url.fragment = \"\";\r\n      this.state = \"fragment\";\r\n    }\r\n  } else {\r\n    // TODO: If c is not a URL code point and not \"%\", parse error.\r\n\r\n    if (c === 37 &&\r\n      (!isASCIIHex(this.input[this.pointer + 1]) ||\r\n        !isASCIIHex(this.input[this.pointer + 2]))) {\r\n      this.parseError = true;\r\n    }\r\n\r\n    this.buffer += percentEncodeChar(c, isPathPercentEncode);\r\n  }\r\n\r\n  return true;\r\n};\r\n\r\nURLStateMachine.prototype[\"parse cannot-be-a-base-URL path\"] = function parseCannotBeABaseURLPath(c) {\r\n  if (c === 63) {\r\n    this.url.query = \"\";\r\n    this.state = \"query\";\r\n  } else if (c === 35) {\r\n    this.url.fragment = \"\";\r\n    this.state = \"fragment\";\r\n  } else {\r\n    // TODO: Add: not a URL code point\r\n    if (!isNaN(c) && c !== 37) {\r\n      this.parseError = true;\r\n    }\r\n\r\n    if (c === 37 &&\r\n        (!isASCIIHex(this.input[this.pointer + 1]) ||\r\n         !isASCIIHex(this.input[this.pointer + 2]))) {\r\n      this.parseError = true;\r\n    }\r\n\r\n    if (!isNaN(c)) {\r\n      this.url.path[0] = this.url.path[0] + percentEncodeChar(c, isC0ControlPercentEncode);\r\n    }\r\n  }\r\n\r\n  return true;\r\n};\r\n\r\nURLStateMachine.prototype[\"parse query\"] = function parseQuery(c, cStr) {\r\n  if (isNaN(c) || (!this.stateOverride && c === 35)) {\r\n    if (!isSpecial(this.url) || this.url.scheme === \"ws\" || this.url.scheme === \"wss\") {\r\n      this.encodingOverride = \"utf-8\";\r\n    }\r\n\r\n    const buffer = new Buffer(this.buffer); // TODO: Use encoding override instead\r\n    for (let i = 0; i < buffer.length; ++i) {\r\n      if (buffer[i] < 0x21 || buffer[i] > 0x7E || buffer[i] === 0x22 || buffer[i] === 0x23 ||\r\n          buffer[i] === 0x3C || buffer[i] === 0x3E) {\r\n        this.url.query += percentEncode(buffer[i]);\r\n      } else {\r\n        this.url.query += String.fromCodePoint(buffer[i]);\r\n      }\r\n    }\r\n\r\n    this.buffer = \"\";\r\n    if (c === 35) {\r\n      this.url.fragment = \"\";\r\n      this.state = \"fragment\";\r\n    }\r\n  } else {\r\n    // TODO: If c is not a URL code point and not \"%\", parse error.\r\n    if (c === 37 &&\r\n      (!isASCIIHex(this.input[this.pointer + 1]) ||\r\n        !isASCIIHex(this.input[this.pointer + 2]))) {\r\n      this.parseError = true;\r\n    }\r\n\r\n    this.buffer += cStr;\r\n  }\r\n\r\n  return true;\r\n};\r\n\r\nURLStateMachine.prototype[\"parse fragment\"] = function parseFragment(c) {\r\n  if (isNaN(c)) { // do nothing\r\n  } else if (c === 0x0) {\r\n    this.parseError = true;\r\n  } else {\r\n    // TODO: If c is not a URL code point and not \"%\", parse error.\r\n    if (c === 37 &&\r\n      (!isASCIIHex(this.input[this.pointer + 1]) ||\r\n        !isASCIIHex(this.input[this.pointer + 2]))) {\r\n      this.parseError = true;\r\n    }\r\n\r\n    this.url.fragment += percentEncodeChar(c, isC0ControlPercentEncode);\r\n  }\r\n\r\n  return true;\r\n};\r\n\r\nfunction serializeURL(url, excludeFragment) {\r\n  let output = url.scheme + \":\";\r\n  if (url.host !== null) {\r\n    output += \"//\";\r\n\r\n    if (url.username !== \"\" || url.password !== \"\") {\r\n      output += url.username;\r\n      if (url.password !== \"\") {\r\n        output += \":\" + url.password;\r\n      }\r\n      output += \"@\";\r\n    }\r\n\r\n    output += serializeHost(url.host);\r\n\r\n    if (url.port !== null) {\r\n      output += \":\" + url.port;\r\n    }\r\n  } else if (url.host === null && url.scheme === \"file\") {\r\n    output += \"//\";\r\n  }\r\n\r\n  if (url.cannotBeABaseURL) {\r\n    output += url.path[0];\r\n  } else {\r\n    for (const string of url.path) {\r\n      output += \"/\" + string;\r\n    }\r\n  }\r\n\r\n  if (url.query !== null) {\r\n    output += \"?\" + url.query;\r\n  }\r\n\r\n  if (!excludeFragment && url.fragment !== null) {\r\n    output += \"#\" + url.fragment;\r\n  }\r\n\r\n  return output;\r\n}\r\n\r\nfunction serializeOrigin(tuple) {\r\n  let result = tuple.scheme + \"://\";\r\n  result += serializeHost(tuple.host);\r\n\r\n  if (tuple.port !== null) {\r\n    result += \":\" + tuple.port;\r\n  }\r\n\r\n  return result;\r\n}\r\n\r\nmodule.exports.serializeURL = serializeURL;\r\n\r\nmodule.exports.serializeURLOrigin = function (url) {\r\n  // https://url.spec.whatwg.org/#concept-url-origin\r\n  switch (url.scheme) {\r\n    case \"blob\":\r\n      try {\r\n        return module.exports.serializeURLOrigin(module.exports.parseURL(url.path[0]));\r\n      } catch (e) {\r\n        // serializing an opaque origin returns \"null\"\r\n        return \"null\";\r\n      }\r\n    case \"ftp\":\r\n    case \"gopher\":\r\n    case \"http\":\r\n    case \"https\":\r\n    case \"ws\":\r\n    case \"wss\":\r\n      return serializeOrigin({\r\n        scheme: url.scheme,\r\n        host: url.host,\r\n        port: url.port\r\n      });\r\n    case \"file\":\r\n      // spec says \"exercise to the reader\", chrome says \"file://\"\r\n      return \"file://\";\r\n    default:\r\n      // serializing an opaque origin returns \"null\"\r\n      return \"null\";\r\n  }\r\n};\r\n\r\nmodule.exports.basicURLParse = function (input, options) {\r\n  if (options === undefined) {\r\n    options = {};\r\n  }\r\n\r\n  const usm = new URLStateMachine(input, options.baseURL, options.encodingOverride, options.url, options.stateOverride);\r\n  if (usm.failure) {\r\n    return \"failure\";\r\n  }\r\n\r\n  return usm.url;\r\n};\r\n\r\nmodule.exports.setTheUsername = function (url, username) {\r\n  url.username = \"\";\r\n  const decoded = punycode.ucs2.decode(username);\r\n  for (let i = 0; i < decoded.length; ++i) {\r\n    url.username += percentEncodeChar(decoded[i], isUserinfoPercentEncode);\r\n  }\r\n};\r\n\r\nmodule.exports.setThePassword = function (url, password) {\r\n  url.password = \"\";\r\n  const decoded = punycode.ucs2.decode(password);\r\n  for (let i = 0; i < decoded.length; ++i) {\r\n    url.password += percentEncodeChar(decoded[i], isUserinfoPercentEncode);\r\n  }\r\n};\r\n\r\nmodule.exports.serializeHost = serializeHost;\r\n\r\nmodule.exports.cannotHaveAUsernamePasswordPort = cannotHaveAUsernamePasswordPort;\r\n\r\nmodule.exports.serializeInteger = function (integer) {\r\n  return String(integer);\r\n};\r\n\r\nmodule.exports.parseURL = function (input, options) {\r\n  if (options === undefined) {\r\n    options = {};\r\n  }\r\n\r\n  // We don't handle blobs, so this just delegates:\r\n  return module.exports.basicURLParse(input, { baseURL: options.baseURL, encodingOverride: options.encodingOverride });\r\n};\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/whatwg-url/lib/url-state-machine.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/whatwg-url/lib/utils.js":
/*!**********************************************!*\
  !*** ./node_modules/whatwg-url/lib/utils.js ***!
  \**********************************************/
/***/ ((module) => {

eval("\n\nmodule.exports.mixin = function mixin(target, source) {\n  const keys = Object.getOwnPropertyNames(source);\n  for (let i = 0; i < keys.length; ++i) {\n    Object.defineProperty(target, keys[i], Object.getOwnPropertyDescriptor(source, keys[i]));\n  }\n};\n\nmodule.exports.wrapperSymbol = Symbol(\"wrapper\");\nmodule.exports.implSymbol = Symbol(\"impl\");\n\nmodule.exports.wrapperForImpl = function (impl) {\n  return impl[module.exports.wrapperSymbol];\n};\n\nmodule.exports.implForWrapper = function (wrapper) {\n  return wrapper[module.exports.implSymbol];\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvd2hhdHdnLXVybC9saWIvdXRpbHMuanMiLCJtYXBwaW5ncyI6IkFBQWE7O0FBRWIsb0JBQW9CO0FBQ3BCO0FBQ0Esa0JBQWtCLGlCQUFpQjtBQUNuQztBQUNBO0FBQ0E7O0FBRUEsNEJBQTRCO0FBQzVCLHlCQUF5Qjs7QUFFekIsNkJBQTZCO0FBQzdCO0FBQ0E7O0FBRUEsNkJBQTZCO0FBQzdCO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9xdW90ZS1haS1jcm0vLi9ub2RlX21vZHVsZXMvd2hhdHdnLXVybC9saWIvdXRpbHMuanM/Y2MwMCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcblxubW9kdWxlLmV4cG9ydHMubWl4aW4gPSBmdW5jdGlvbiBtaXhpbih0YXJnZXQsIHNvdXJjZSkge1xuICBjb25zdCBrZXlzID0gT2JqZWN0LmdldE93blByb3BlcnR5TmFtZXMoc291cmNlKTtcbiAgZm9yIChsZXQgaSA9IDA7IGkgPCBrZXlzLmxlbmd0aDsgKytpKSB7XG4gICAgT2JqZWN0LmRlZmluZVByb3BlcnR5KHRhcmdldCwga2V5c1tpXSwgT2JqZWN0LmdldE93blByb3BlcnR5RGVzY3JpcHRvcihzb3VyY2UsIGtleXNbaV0pKTtcbiAgfVxufTtcblxubW9kdWxlLmV4cG9ydHMud3JhcHBlclN5bWJvbCA9IFN5bWJvbChcIndyYXBwZXJcIik7XG5tb2R1bGUuZXhwb3J0cy5pbXBsU3ltYm9sID0gU3ltYm9sKFwiaW1wbFwiKTtcblxubW9kdWxlLmV4cG9ydHMud3JhcHBlckZvckltcGwgPSBmdW5jdGlvbiAoaW1wbCkge1xuICByZXR1cm4gaW1wbFttb2R1bGUuZXhwb3J0cy53cmFwcGVyU3ltYm9sXTtcbn07XG5cbm1vZHVsZS5leHBvcnRzLmltcGxGb3JXcmFwcGVyID0gZnVuY3Rpb24gKHdyYXBwZXIpIHtcbiAgcmV0dXJuIHdyYXBwZXJbbW9kdWxlLmV4cG9ydHMuaW1wbFN5bWJvbF07XG59O1xuXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/whatwg-url/lib/utils.js\n");

/***/ })

};
;