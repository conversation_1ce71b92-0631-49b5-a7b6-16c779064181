"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/register/page",{

/***/ "(app-pages-browser)/./lib/auth.ts":
/*!*********************!*\
  !*** ./lib/auth.ts ***!
  \*********************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createServerClient: function() { return /* binding */ createServerClient; },\n/* harmony export */   getCurrentUser: function() { return /* binding */ getCurrentUser; },\n/* harmony export */   signIn: function() { return /* binding */ signIn; },\n/* harmony export */   signOut: function() { return /* binding */ signOut; },\n/* harmony export */   signUp: function() { return /* binding */ signUp; }\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(app-pages-browser)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\n// Create Supabase client for client-side usage\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(\"https://your-project.supabase.co\", \"your-public-anon-key\");\n// Create Supabase client for server-side usage\nfunction createServerClient() {\n    return (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(\"https://your-project.supabase.co\", \"your-public-anon-key\");\n}\nasync function getCurrentUser() {\n    try {\n        const { data: { user }, error } = await supabase.auth.getUser();\n        if (error || !user) {\n            return null;\n        }\n        // Get additional user data from your users table if needed\n        const { data: userData, error: userError } = await supabase.from(\"users\").select(\"full_name, pricing_tier, plan\").eq(\"id\", user.id).single();\n        return {\n            id: user.id,\n            email: user.email,\n            full_name: userData === null || userData === void 0 ? void 0 : userData.full_name,\n            pricing_tier: userData === null || userData === void 0 ? void 0 : userData.pricing_tier,\n            plan: userData === null || userData === void 0 ? void 0 : userData.plan\n        };\n    } catch (error) {\n        console.error(\"Error getting current user:\", error);\n        return null;\n    }\n}\nasync function signOut() {\n    const { error } = await supabase.auth.signOut();\n    if (error) {\n        console.error(\"Error signing out:\", error);\n        throw error;\n    }\n}\nasync function signIn(email, password) {\n    const { data, error } = await supabase.auth.signInWithPassword({\n        email,\n        password\n    });\n    if (error) {\n        throw error;\n    }\n    return data;\n}\nasync function signUp(email, password, fullName) {\n    const { data, error } = await supabase.auth.signUp({\n        email,\n        password,\n        options: {\n            data: {\n                full_name: fullName\n            }\n        }\n    });\n    if (error) {\n        throw error;\n    }\n    return data;\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/auth.ts\n"));

/***/ })

});