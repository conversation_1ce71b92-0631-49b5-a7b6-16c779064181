"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/register/page",{

/***/ "(app-pages-browser)/./lib/auth.ts":
/*!*********************!*\
  !*** ./lib/auth.ts ***!
  \*********************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createServerClient: function() { return /* binding */ createServerClient; },\n/* harmony export */   getCurrentUser: function() { return /* binding */ getCurrentUser; },\n/* harmony export */   signIn: function() { return /* binding */ signIn; },\n/* harmony export */   signOut: function() { return /* binding */ signOut; },\n/* harmony export */   signUp: function() { return /* binding */ signUp; }\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(app-pages-browser)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\n// Create Supabase client for client-side usage\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(\"https://your-project.supabase.co\", \"your-public-anon-key\");\n// Create Supabase client for server-side usage\nfunction createServerClient() {\n    return (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(\"https://your-project.supabase.co\", \"your-public-anon-key\");\n}\nasync function getCurrentUser() {\n    try {\n        var _user_user_metadata, _user_email, _user_user_metadata1, _user_user_metadata2;\n        const serverClient = createServerClient();\n        const { data: { user }, error } = await serverClient.auth.getUser();\n        if (error || !user) {\n            return null;\n        }\n        // For now, return basic user info without additional database queries\n        // You can extend this later when you have a users table set up\n        return {\n            id: user.id,\n            email: user.email,\n            full_name: ((_user_user_metadata = user.user_metadata) === null || _user_user_metadata === void 0 ? void 0 : _user_user_metadata.full_name) || ((_user_email = user.email) === null || _user_email === void 0 ? void 0 : _user_email.split(\"@\")[0]),\n            pricing_tier: ((_user_user_metadata1 = user.user_metadata) === null || _user_user_metadata1 === void 0 ? void 0 : _user_user_metadata1.pricing_tier) || \"Free\",\n            plan: ((_user_user_metadata2 = user.user_metadata) === null || _user_user_metadata2 === void 0 ? void 0 : _user_user_metadata2.plan) || \"Free\"\n        };\n    } catch (error) {\n        console.error(\"Error getting current user:\", error);\n        return null;\n    }\n}\nasync function signOut() {\n    const { error } = await supabase.auth.signOut();\n    if (error) {\n        console.error(\"Error signing out:\", error);\n        throw error;\n    }\n}\nasync function signIn(email, password) {\n    const { data, error } = await supabase.auth.signInWithPassword({\n        email,\n        password\n    });\n    if (error) {\n        throw error;\n    }\n    return data;\n}\nasync function signUp(email, password, fullName) {\n    const { data, error } = await supabase.auth.signUp({\n        email,\n        password,\n        options: {\n            data: {\n                full_name: fullName\n            }\n        }\n    });\n    if (error) {\n        throw error;\n    }\n    return data;\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/auth.ts\n"));

/***/ })

});