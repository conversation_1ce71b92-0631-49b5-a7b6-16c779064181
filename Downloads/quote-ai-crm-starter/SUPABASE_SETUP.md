# Supabase Setup Instructies

## Stap 1: Supabase Project Aanmaken

1. Ga naar [supabase.com](https://supabase.com)
2. <PERSON><PERSON> op "Start your project"
3. Log in met je GitHub account
4. <PERSON><PERSON> op "New Project"
5. <PERSON><PERSON> een organisatie (of maak een nieuwe aan)
6. Vul de project details in:
   - **Name**: `quote-ai-crm`
   - **Database Password**: <PERSON><PERSON> een sterk wachtwoord
   - **Region**: <PERSON><PERSON> de dichtst<PERSON> regio (bijv. West EU (Ireland))
7. <PERSON><PERSON> op "Create new project"

## Stap 2: API Keys Ophalen

1. Ga naar je project dashboard
2. Klik op het tandwiel icoon (Settings) in de linker sidebar
3. Klik op "API" in het settings menu
4. Kopieer de volgende waarden:
   - **Project URL** (onder "Project URL")
   - **anon public** key (onder "Project API keys")
   - **service_role** key (onder "Project API keys") - **Houd deze geheim!**

## Stap 3: Environment Variables Configureren

Open je `.env.local` bestand en vervang de placeholder waarden:

```env
# === Supabase Config ===
NEXT_PUBLIC_SUPABASE_URL=https://jouwprojectid.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

## Stap 4: Authentication Inschakelen

1. Ga naar "Authentication" in de linker sidebar van je Supabase dashboard
2. Klik op "Settings" tab
3. Scroll naar "Site URL" en voeg toe: `http://localhost:3002`
4. Scroll naar "Redirect URLs" en voeg toe: `http://localhost:3002/auth/callback`

## Stap 5: Database Schema (Optioneel)

Als je later gebruikersprofielen wilt uitbreiden, kun je deze SQL uitvoeren in de SQL Editor:

```sql
-- Create a users table for additional user data
CREATE TABLE users (
  id UUID REFERENCES auth.users(id) PRIMARY KEY,
  full_name TEXT,
  pricing_tier TEXT DEFAULT 'Free',
  plan TEXT DEFAULT 'Free',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable Row Level Security
ALTER TABLE users ENABLE ROW LEVEL SECURITY;

-- Create policy so users can only see their own data
CREATE POLICY "Users can view own profile" ON users
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON users
  FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can insert own profile" ON users
  FOR INSERT WITH CHECK (auth.uid() = id);

-- Function to automatically create user profile
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.users (id, full_name)
  VALUES (NEW.id, NEW.raw_user_meta_data->>'full_name');
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to create profile on signup
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();
```

## Stap 6: Test de Setup

1. Herstart je development server: `npm run dev`
2. Ga naar `http://localhost:3002/register`
3. Probeer een account aan te maken
4. Check je email voor de verificatie link
5. Probeer in te loggen op `http://localhost:3002/login`

## Troubleshooting

### "Invalid API key" error
- Controleer of je de juiste API keys hebt gekopieerd
- Zorg ervoor dat er geen extra spaties zijn in je .env.local bestand

### "Invalid login credentials" error
- Zorg ervoor dat je email is geverifieerd
- Check je Supabase dashboard onder Authentication > Users

### Email verificatie werkt niet
- Check je spam folder
- Ga naar Authentication > Settings in Supabase
- Zorg ervoor dat "Confirm email" is ingeschakeld
- Voor development kun je dit tijdelijk uitschakelen

## Productie Setup

Voor productie moet je ook:
1. Je productie URL toevoegen aan "Site URL" en "Redirect URLs"
2. Email templates aanpassen onder Authentication > Email Templates
3. Custom SMTP configureren voor betrouwbare email delivery
