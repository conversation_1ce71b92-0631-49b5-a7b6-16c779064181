import { redirect } from "next/navigation";
import { getCurrentUser } from "@/lib/auth";
export default async function DashboardPage() {
  const user = await getCurrentUser();
  if (!user) redirect("/login");
  return (
    <div className="min-h-screen bg-slate-950 text-white p-8">
      <h1 className="text-3xl font-bold mb-4"><PERSON><PERSON><PERSON>, {user.full_name || user.email}</h1>
      <p className="text-white/70">Je zit in het <strong>{user.pricing_tier || user.plan || "Free"}</strong> pakket.</p>
    </div>
  );
}