import { redirect } from "next/navigation";
import { getCurrentUser } from "@/lib/auth";
export default async function Page() {
  const user = await getCurrentUser();
  if (!user) redirect("/login");
  return (
    <main className="min-h-screen bg-slate-950 text-white p-8">
      <h1 className="text-2xl font-bold mb-4"><PERSON><PERSON> Offertes</h1>
      <p className="text-white/70">Content volgt…</p>
    </main>
  );
}