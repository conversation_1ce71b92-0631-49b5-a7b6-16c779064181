"use client";
import React from "react";
import { startStripeCheckout } from "@/lib/checkoutStripe";
export default function Home() {
  return (
    <main className="min-h-screen bg-gradient-to-br from-[#0f0c29] via-[#302b63] to-[#24243e] text-white font-sans">
      <section className="text-center py-24 px-6">
        <h1 className="text-4xl md:text-6xl font-bold mb-4">Offertes in 60 seconden met AI-assistent <span className="text-fuchsia-400">Rima</span></h1>
        <p className="text-lg md:text-xl mb-8 text-white/80">Be<PERSON>aar tijd, win klanten en beheer je bouwbedrijf volledig online.</p>
        <div className="flex justify-center gap-4">
          <button onClick={() => startStripeCheckout("starter")} className="px-6 py-3 rounded-xl bg-gradient-to-r from-blue-500 to-fuchsia-500 text-white font-semibold shadow-xl">Start Gratis Proefperiode</button>
          <a href="#demo" className="px-6 py-3 rounded-xl border border-white/20 hover:bg-white/10">Bekijk Demo</a>
        </div>
        <p className="mt-4 text-sm text-white/50">14 dagen gratis • Geen kredietkaart nodig • Daarna automatisch €24/maand via Stripe</p>
      </section>
      <section className="max-w-5xl mx-auto grid grid-cols-2 md:grid-cols-4 gap-6 px-6 py-12 text-center">
        {["+2500", "+18u/week", "93%", "60s"].map((stat, i) => (
          <div key={i} className="bg-white/5 border border-white/10 p-6 rounded-2xl backdrop-blur-xl">
            <div className="text-3xl font-bold text-fuchsia-300">{stat}</div>
            <div className="text-white/70 text-sm mt-2">{["Gebruikers", "Tijdswinst", "Tevredenheid", "Offerte klaar"][i]}</div>
          </div>
        ))}
      </section>
      <section className="py-20 px-6 max-w-6xl mx-auto">
        <h2 className="text-3xl font-bold text-center mb-6">Pakketten & Prijzen</h2>
        <p className="text-center text-white/70 mb-12">Start gratis. Groepeer je offertes, klanten en projecten.</p>
        <div className="grid md:grid-cols-4 gap-6">
          {[
            { title: "Free", price: "€0", features: ["Max 3 offertes", "E-mail verzending"], button: "Start Gratis", link: "/register" },
            { title: "Starter", price: "€24/m", features: ["Tot 20 offertes/maand", "E-mail opvolging", "Basis CRM"], button: "14 dagen gratis proef • Daarna €24/m via Stripe", plan: "starter" },
            { title: "Professional", price: "€49/m", features: ["Alles van Starter", "PDF facturen", "AI suggesties"], button: "Start Professional", plan: "pro" },
            { title: "Enterprise", price: "Custom", features: ["Volledige automatisatie", "Eigen domein", "24/7 support"], button: "Contact Sales", link: "/contact" },
          ].map((p, i) => (
            <div key={i} className="bg-white/5 border border-white/10 p-6 rounded-2xl backdrop-blur-xl">
              <h3 className="text-xl font-semibold mb-2">{p.title}</h3>
              <div className="text-3xl font-bold mb-4">{p.price}</div>
              <ul className="text-sm text-white/80 space-y-2 mb-6">
                {p.features.map((f, j) => (<li key={j}>✓ {f}</li>))}
              </ul>
              {"plan" in p ? (
                <button onClick={() => startStripeCheckout(p.plan as string)} className="block w-full px-4 py-2 text-sm rounded-lg text-center bg-gradient-to-r from-blue-500 to-fuchsia-500 text-white shadow-md">{p.button}</button>
              ) : (
                <a href={p.link!} className="block w-full px-4 py-2 text-sm rounded-lg text-center bg-gradient-to-r from-blue-500 to-fuchsia-500 text-white shadow-md">{p.button}</a>
              )}
            </div>
          ))}
        </div>
      </section>
      <section id="demo" className="py-20 px-6 max-w-5xl mx-auto">
        <h2 className="text-3xl font-bold text-center mb-10">Hoe Rima jouw offerte maakt</h2>
        <div className="grid md:grid-cols-4 gap-6">
          {["Omschrijf je project", "AI maakt een voorstel", "Pas aan indien nodig", "Genereer PDF & verzend"].map((step, i) => (
            <div key={i} className="bg-white/5 border border-white/10 p-6 rounded-2xl backdrop-blur-xl text-center">
              <div className="text-4xl font-bold text-blue-300 mb-2">{i + 1}</div>
              <div className="text-sm text-white/80">{step}</div>
            </div>
          ))}
        </div>
      </section>
      <footer className="bg-white/5 border-t border-white/10 py-10 px-6 text-center text-sm text-white/60">
        <p>© {new Date().getFullYear()} Quote.AI+CRM — Rima</p>
        <div className="mt-3 flex justify-center gap-4">
          <a href="#" className="hover:underline">Support</a>
          <a href="#" className="hover:underline">Privacy</a>
          <a href="#" className="hover:underline">Terms</a>
        </div>
      </footer>
    </main>
  );
}