"use client";
import React, { useState, useEffect } from "react";
import { createClient } from '@supabase/supabase-js';
import Link from "next/link";

export default function SetupPage() {
  const [supabaseStatus, setSupabaseStatus] = useState<'checking' | 'connected' | 'error'>('checking');
  const [errorMessage, setErrorMessage] = useState('');

  useEffect(() => {
    checkSupabaseConnection();
  }, []);

  const checkSupabaseConnection = async () => {
    try {
      const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
      const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

      if (!supabaseUrl || !supabaseKey || 
          supabaseUrl.includes('your-project') || 
          supabaseKey.includes('your-public-anon-key')) {
        setSupabaseStatus('error');
        setErrorMessage('Supabase environment variables zijn nog niet geconfigureerd');
        return;
      }

      const supabase = createClient(supabaseUrl, supabaseKey);
      
      // Test the connection
      const { data, error } = await supabase.auth.getSession();
      
      if (error) {
        setSupabaseStatus('error');
        setErrorMessage(`Supabase connection error: ${error.message}`);
      } else {
        setSupabaseStatus('connected');
      }
    } catch (error: any) {
      setSupabaseStatus('error');
      setErrorMessage(`Connection test failed: ${error.message}`);
    }
  };

  const getStatusColor = () => {
    switch (supabaseStatus) {
      case 'checking': return 'text-yellow-400';
      case 'connected': return 'text-green-400';
      case 'error': return 'text-red-400';
    }
  };

  const getStatusText = () => {
    switch (supabaseStatus) {
      case 'checking': return 'Verbinding testen...';
      case 'connected': return 'Verbonden ✓';
      case 'error': return 'Fout ✗';
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#0f0c29] via-[#302b63] to-[#24243e] p-8">
      <div className="max-w-4xl mx-auto">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-white mb-4">Setup & Configuratie</h1>
          <p className="text-white/70">Controleer of alle services correct zijn geconfigureerd</p>
        </div>

        <div className="grid gap-6">
          {/* Supabase Status */}
          <div className="bg-white/10 backdrop-blur-xl border border-white/20 rounded-2xl p-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-2xl font-semibold text-white">Supabase Database</h2>
              <span className={`font-medium ${getStatusColor()}`}>
                {getStatusText()}
              </span>
            </div>
            
            {supabaseStatus === 'error' && (
              <div className="bg-red-500/20 border border-red-500/30 rounded-lg p-4 mb-4">
                <p className="text-red-200 text-sm">{errorMessage}</p>
              </div>
            )}

            <div className="space-y-3 text-white/80">
              <div className="flex items-center gap-3">
                <div className={`w-3 h-3 rounded-full ${
                  process.env.NEXT_PUBLIC_SUPABASE_URL && 
                  !process.env.NEXT_PUBLIC_SUPABASE_URL.includes('your-project') 
                    ? 'bg-green-400' : 'bg-red-400'
                }`}></div>
                <span>NEXT_PUBLIC_SUPABASE_URL</span>
              </div>
              <div className="flex items-center gap-3">
                <div className={`w-3 h-3 rounded-full ${
                  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY && 
                  !process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY.includes('your-public-anon-key') 
                    ? 'bg-green-400' : 'bg-red-400'
                }`}></div>
                <span>NEXT_PUBLIC_SUPABASE_ANON_KEY</span>
              </div>
            </div>

            {supabaseStatus === 'error' && (
              <div className="mt-4">
                <Link 
                  href="/SUPABASE_SETUP.md" 
                  target="_blank"
                  className="inline-flex items-center px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors"
                >
                  📖 Setup Instructies
                </Link>
              </div>
            )}
          </div>

          {/* Environment Variables Check */}
          <div className="bg-white/10 backdrop-blur-xl border border-white/20 rounded-2xl p-6">
            <h2 className="text-2xl font-semibold text-white mb-4">Environment Variables</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              {[
                'NEXT_PUBLIC_SUPABASE_URL',
                'NEXT_PUBLIC_SUPABASE_ANON_KEY',
                'STRIPE_SECRET_KEY',
                'NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY',
                'NEXT_PUBLIC_SITE_URL'
              ].map((envVar) => {
                const value = process.env[envVar];
                const isConfigured = value && !value.includes('your-') && !value.includes('sk_test_your') && !value.includes('pk_test_your');
                
                return (
                  <div key={envVar} className="flex items-center gap-3">
                    <div className={`w-3 h-3 rounded-full ${isConfigured ? 'bg-green-400' : 'bg-yellow-400'}`}></div>
                    <span className="text-white/80 font-mono">{envVar}</span>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Quick Actions */}
          <div className="bg-white/10 backdrop-blur-xl border border-white/20 rounded-2xl p-6">
            <h2 className="text-2xl font-semibold text-white mb-4">Quick Actions</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Link 
                href="/register" 
                className="block p-4 bg-blue-500/20 hover:bg-blue-500/30 border border-blue-500/30 rounded-lg text-center text-white transition-colors"
              >
                🔐 Test Registratie
              </Link>
              <Link 
                href="/login" 
                className="block p-4 bg-green-500/20 hover:bg-green-500/30 border border-green-500/30 rounded-lg text-center text-white transition-colors"
              >
                🚪 Test Login
              </Link>
              <Link 
                href="/" 
                className="block p-4 bg-purple-500/20 hover:bg-purple-500/30 border border-purple-500/30 rounded-lg text-center text-white transition-colors"
              >
                🏠 Homepage
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
