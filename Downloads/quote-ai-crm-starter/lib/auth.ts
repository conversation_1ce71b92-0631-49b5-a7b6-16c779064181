import { createClient } from '@supabase/supabase-js';
import { cookies } from 'next/headers';

// Create Supabase client
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
);

export interface User {
  id: string;
  email: string;
  full_name?: string;
  pricing_tier?: string;
  plan?: string;
}

export async function getCurrentUser(): Promise<User | null> {
  try {
    const { data: { user }, error } = await supabase.auth.getUser();
    
    if (error || !user) {
      return null;
    }

    // Get additional user data from your users table if needed
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('full_name, pricing_tier, plan')
      .eq('id', user.id)
      .single();

    return {
      id: user.id,
      email: user.email!,
      full_name: userData?.full_name,
      pricing_tier: userData?.pricing_tier,
      plan: userData?.plan,
    };
  } catch (error) {
    console.error('Error getting current user:', error);
    return null;
  }
}

export async function signOut() {
  const { error } = await supabase.auth.signOut();
  if (error) {
    console.error('Error signing out:', error);
    throw error;
  }
}

export async function signIn(email: string, password: string) {
  const { data, error } = await supabase.auth.signInWithPassword({
    email,
    password,
  });
  
  if (error) {
    throw error;
  }
  
  return data;
}

export async function signUp(email: string, password: string, fullName?: string) {
  const { data, error } = await supabase.auth.signUp({
    email,
    password,
    options: {
      data: {
        full_name: fullName,
      },
    },
  });
  
  if (error) {
    throw error;
  }
  
  return data;
}
