import { createClient } from '@supabase/supabase-js';

// Create Supabase client for client-side usage
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
);

// Create Supabase client for server-side usage
export function createServerClient() {
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  );
}

export interface User {
  id: string;
  email: string;
  full_name?: string;
  pricing_tier?: string;
  plan?: string;
}

export async function getCurrentUser(): Promise<User | null> {
  try {
    const serverClient = createServerClient();
    const { data: { user }, error } = await serverClient.auth.getUser();

    if (error || !user) {
      return null;
    }

    // For now, return basic user info without additional database queries
    // You can extend this later when you have a users table set up
    return {
      id: user.id,
      email: user.email!,
      full_name: user.user_metadata?.full_name || user.email?.split('@')[0],
      pricing_tier: user.user_metadata?.pricing_tier || 'Free',
      plan: user.user_metadata?.plan || 'Free',
    };
  } catch (error) {
    console.error('Error getting current user:', error);
    return null;
  }
}

export async function signOut() {
  const { error } = await supabase.auth.signOut();
  if (error) {
    console.error('Error signing out:', error);
    throw error;
  }
}

export async function signIn(email: string, password: string) {
  const { data, error } = await supabase.auth.signInWithPassword({
    email,
    password,
  });
  
  if (error) {
    throw error;
  }
  
  return data;
}

export async function signUp(email: string, password: string, fullName?: string) {
  const { data, error } = await supabase.auth.signUp({
    email,
    password,
    options: {
      data: {
        full_name: fullName,
      },
    },
  });
  
  if (error) {
    throw error;
  }
  
  return data;
}
