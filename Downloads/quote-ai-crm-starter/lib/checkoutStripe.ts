import { loadStripe } from '@stripe/stripe-js';

// Initialize Stripe
const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!);

export async function startStripeCheckout(plan: string) {
  try {
    const stripe = await stripePromise;
    
    if (!stripe) {
      throw new Error('<PERSON><PERSON> failed to initialize');
    }

    // Create checkout session
    const response = await fetch('/api/create-checkout-session', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        plan,
      }),
    });

    if (!response.ok) {
      throw new Error('Failed to create checkout session');
    }

    const { sessionId } = await response.json();

    // Redirect to Stripe Checkout
    const { error } = await stripe.redirectToCheckout({
      sessionId,
    });

    if (error) {
      console.error('Stripe checkout error:', error);
      throw error;
    }
  } catch (error) {
    console.error('Checkout error:', error);
    // You might want to show a toast notification here
    alert('Er is een fout opgetreden bij het starten van de checkout. Probeer het opnieuw.');
  }
}
